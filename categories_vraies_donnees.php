<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vraies Catégories HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        
        .zone-repetee {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .ligne-categorie {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .ligne-categorie:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .photo-categorie {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .no-photo {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        .nom-categorie {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }
        
        .id-categorie {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            text-align: center;
            width: 100%;
        }
        
        .compteur {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📂 Vraies Catégories de la base DataCafe</h1>

        <?php
        // Fonction pour charger les vraies catégories
        function chargerVraiesCategories() {
            // Option 1 : Depuis l'API WebDev (si créée)
            $apiUrl = 'http://localhost:8080/categories.awp';
            $categories = chargerDepuisAPI($apiUrl);
            if ($categories !== false) {
                return $categories;
            }
            
            // Option 2 : Depuis un fichier JSON exporté
            $fichierExport = 'categories_reelles.json';
            if (file_exists($fichierExport)) {
                $contenu = file_get_contents($fichierExport);
                $categories = json_decode($contenu, true);
                if (is_array($categories)) {
                    return $categories;
                }
            }
            
            // Option 3 : Tentative de connexion ODBC directe (si jamais ça marche)
            $categories = chargerDepuisODBC();
            if ($categories !== false) {
                return $categories;
            }
            
            // Aucune source de données réelle disponible
            return false;
        }
        
        // Fonction pour charger depuis l'API WebDev
        function chargerDepuisAPI($url) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'method' => 'GET',
                        'header' => 'Accept: application/json',
                        'timeout' => 5
                    ]
                ]);
                
                $response = @file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    if (isset($data['error']) && !$data['error'] && isset($data['data'])) {
                        return $data['data'];
                    }
                }
            } catch (Exception $e) {
                // Silencieux
            }
            
            return false;
        }
        
        // Fonction pour charger depuis ODBC (au cas où)
        function chargerDepuisODBC() {
            if (!extension_loaded('odbc')) {
                return false;
            }
            
            $dsn = "Driver={HFSQL};Server=127.0.0.1;Port=4900;Database=DataCafe;UID=admin;PWD=";
            $connection = @odbc_connect($dsn, "admin", "");
            
            if (!$connection) {
                return false;
            }
            
            $categories = [];
            $result = @odbc_exec($connection, "SELECT IDCategorie, categories, photo FROM Categorie");
            
            if ($result) {
                while (odbc_fetch_row($result)) {
                    $categories[] = [
                        'IDCategorie' => odbc_result($result, 'IDCategorie'),
                        'categories' => odbc_result($result, 'categories'),
                        'photo' => odbc_result($result, 'photo')
                    ];
                }
                odbc_free_result($result);
            }
            
            odbc_close($connection);
            return empty($categories) ? false : $categories;
        }
        
        // === CHARGEMENT DES DONNÉES ===
        
        echo "<div class='info'>";
        echo "<h2>🔍 Recherche des vraies données...</h2>";
        echo "</div>";
        
        $categories_reelles = chargerVraiesCategories();
        
        if ($categories_reelles === false) {
            echo "<div class='error'>";
            echo "<h3>❌ Aucune source de données réelle trouvée</h3>";
            echo "<p>Les vraies catégories ne sont pas accessibles pour le moment.</p>";
            echo "</div>";
            
            echo "<div class='instructions'>";
            echo "<h3>📋 Pour récupérer vos vraies catégories :</h3>";
            echo "<ol>";
            echo "<li><strong>Option 1 - API WebDev :</strong>";
            echo "<ul>";
            echo "<li>Créez l'API WebDev avec le guide fourni</li>";
            echo "<li>Déployez sur le port 8080</li>";
            echo "<li>L'API sera automatiquement détectée</li>";
            echo "</ul></li>";
            
            echo "<li><strong>Option 2 - Export JSON :</strong>";
            echo "<ul>";
            echo "<li>Exécutez le code WinDev fourni dans votre application</li>";
            echo "<li>Il créera le fichier 'categories_reelles.json'</li>";
            echo "<li>Placez ce fichier dans le même dossier que ce script</li>";
            echo "</ul></li>";
            
            echo "<li><strong>Option 3 - Résoudre ODBC :</strong>";
            echo "<ul>";
            echo "<li>Configurez correctement l'accès ODBC à HFSQL</li>";
            echo "<li>Le script tentera une connexion directe</li>";
            echo "</ul></li>";
            echo "</ol>";
            echo "</div>";
            
            // Afficher un exemple de ce à quoi ça ressemblera
            echo "<div class='warning'>";
            echo "<h3>📋 Aperçu avec données de simulation</h3>";
            echo "<p>Voici à quoi ressemblera l'affichage avec vos vraies données :</p>";
            echo "</div>";
            
            // Données de simulation pour l'exemple
            $categories_reelles = [
                ['IDCategorie' => 1, 'categories' => '[Exemple] Vos vraies catégories apparaîtront ici', 'photo' => ''],
                ['IDCategorie' => 2, 'categories' => '[Exemple] Avec les vrais noms de votre base', 'photo' => ''],
            ];
        } else {
            echo "<div class='success'>";
            echo "<h3>✅ Vraies données chargées avec succès !</h3>";
            echo "<p>Source : " . (file_exists('categories_reelles.json') ? 'Fichier JSON exporté' : 'API WebDev ou ODBC') . "</p>";
            echo "</div>";
        }
        
        // === AFFICHAGE DES CATÉGORIES ===
        
        $ZR_cat = [];
        
        // POUR TOUT Categorie (avec vraies données)
        foreach ($categories_reelles as $Categorie) {
            $ZR_cat[] = [
                'nom' => $Categorie['categories'],
                'photo' => $Categorie['photo'],
                'id' => $Categorie['IDCategorie']
            ];
        }
        
        echo "<div class='compteur'>";
        echo "📊 Nombre de vraies catégories : " . count($ZR_cat);
        echo "</div>";
        
        echo "<h2>📂 Vos vraies catégories HFSQL</h2>";
        
        if (empty($ZR_cat)) {
            echo "<div class='error'>Aucune catégorie trouvée</div>";
        } else {
            echo "<div class='zone-repetee'>";
            
            foreach ($ZR_cat as $ligne) {
                echo "<div class='ligne-categorie'>";
                
                // Photo
                if (!empty($ligne['photo']) && file_exists($ligne['photo'])) {
                    echo "<img src='" . htmlspecialchars($ligne['photo']) . "' alt='" . htmlspecialchars($ligne['nom']) . "' class='photo-categorie'>";
                } else {
                    echo "<div class='no-photo'>📂</div>";
                }
                
                // Nom
                echo "<div class='nom-categorie'>" . htmlspecialchars($ligne['nom']) . "</div>";
                
                // ID
                echo "<div class='id-categorie'>ID: " . htmlspecialchars($ligne['id']) . "</div>";
                
                echo "</div>";
            }
            
            echo "</div>";
        }
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>🔄 Actions</h3>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="api_webdev_guide.md" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">📖 Guide API WebDev</a>
                <a href="export_categories_windev.wl" style="background-color: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📤 Code Export WinDev</a>
            </p>
        </div>
    </div>
</body>
</html>
