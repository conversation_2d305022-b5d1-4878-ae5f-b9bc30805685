<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connexion HFSQL avec PDO</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test de Connexion HFSQL avec PDO</h1>

        <?php
        // Configuration HFSQL
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => '',
            'driver' => 'HFSQL'
        ];

        echo "<div class='test-section'>";
        echo "<h2>📋 Configuration</h2>";
        echo "<ul>";
        echo "<li><strong>Serveur:</strong> {$config['server']}</li>";
        echo "<li><strong>Port:</strong> {$config['port']}</li>";
        echo "<li><strong>Base de données:</strong> {$config['database']}</li>";
        echo "<li><strong>Utilisateur:</strong> {$config['username']}</li>";
        echo "<li><strong>Driver ODBC:</strong> {$config['driver']}</li>";
        echo "</ul>";
        echo "</div>";

        // Test 1: Vérification des extensions PHP
        echo "<div class='test-section'>";
        echo "<h2>🔧 Extensions PHP</h2>";
        
        $extensions = ['odbc', 'pdo', 'pdo_odbc'];
        foreach ($extensions as $ext) {
            if (extension_loaded($ext)) {
                echo "<div class='success'>✅ Extension <code>$ext</code> est chargée</div>";
            } else {
                echo "<div class='error'>❌ Extension <code>$ext</code> n'est pas chargée</div>";
            }
        }
        echo "</div>";

        // Test 2: Test de connectivité réseau
        echo "<div class='test-section'>";
        echo "<h2>🌐 Test de Connectivité Réseau</h2>";
        
        $connection = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($connection) {
            fclose($connection);
            echo "<div class='success'>✅ Connexion réseau réussie vers {$config['server']}:{$config['port']}</div>";
        } else {
            echo "<div class='error'>❌ Impossible de se connecter à {$config['server']}:{$config['port']}</div>";
            echo "<div class='warning'>Erreur: $errstr ($errno)</div>";
        }
        echo "</div>";

        // Test 3: Test avec PDO ODBC
        echo "<div class='test-section'>";
        echo "<h2>🔌 Test de Connexion HFSQL avec PDO</h2>";

        if (extension_loaded('pdo_odbc')) {
            $dsn = "odbc:Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']}";
            
            echo "<div class='info'><strong>DSN PDO utilisé:</strong><br><code>$dsn</code></div>";

            try {
                $start_time = microtime(true);
                $pdo = new PDO($dsn, $config['username'], $config['password']);
                $connection_time = round((microtime(true) - $start_time) * 1000, 2);

                echo "<div class='success'>✅ <strong>Connexion PDO réussie!</strong></div>";
                echo "<div class='info'>Temps de connexion: {$connection_time}ms</div>";

                // Test d'une requête simple
                try {
                    $stmt = $pdo->query("SELECT 1 as test");
                    if ($stmt) {
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        echo "<div class='success'>✅ Test de requête réussi: " . json_encode($result) . "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ Erreur lors du test de requête: " . $e->getMessage() . "</div>";
                }

                // Tentative de liste des tables
                try {
                    $stmt = $pdo->query("SHOW TABLES");
                    if ($stmt) {
                        echo "<div class='success'>✅ Liste des tables accessible</div>";
                        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        if (!empty($tables)) {
                            echo "<div class='info'><strong>Tables trouvées:</strong><br>";
                            foreach ($tables as $table) {
                                echo "- $table<br>";
                            }
                            echo "</div>";
                        }
                    }
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ Impossible de lister les tables: " . $e->getMessage() . "</div>";
                    echo "<div class='info'>Cela peut être normal selon la configuration de sécurité HFSQL</div>";
                }

            } catch (PDOException $e) {
                echo "<div class='error'>❌ <strong>Erreur de connexion PDO:</strong><br>" . $e->getMessage() . "</div>";
                
                echo "<h3>🔧 Suggestions de dépannage:</h3>";
                echo "<ul>";
                echo "<li>Vérifiez que le serveur HFSQL WinDev est démarré</li>";
                echo "<li>Vérifiez l'adresse IP et le port du serveur (actuellement: {$config['server']}:{$config['port']})</li>";
                echo "<li>Vérifiez que le driver HFSQL ODBC est installé et configuré</li>";
                echo "<li>Vérifiez les identifiants de connexion (utilisateur: {$config['username']})</li>";
                echo "<li>Vérifiez que la base de données '{$config['database']}' existe</li>";
                echo "<li>Vérifiez les permissions de l'utilisateur sur la base</li>";
                echo "<li>Vérifiez le pare-feu (port {$config['port']} doit être ouvert)</li>";
                echo "<li>Redémarrez le serveur web Apache après avoir modifié php.ini</li>";
                echo "</ul>";
            }
        } else {
            echo "<div class='error'>❌ L'extension PDO_ODBC n'est pas disponible</div>";
        }
        echo "</div>";

        // Test 4: Test avec ODBC classique (fallback)
        echo "<div class='test-section'>";
        echo "<h2>🔌 Test de Connexion HFSQL avec ODBC classique</h2>";

        if (extension_loaded('odbc')) {
            $dsn = "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";
            
            echo "<div class='info'><strong>DSN ODBC utilisé:</strong><br><code>$dsn</code></div>";

            try {
                $start_time = microtime(true);
                $connection = odbc_connect($dsn, $config['username'], $config['password']);
                $connection_time = round((microtime(true) - $start_time) * 1000, 2);

                if (!$connection) {
                    $error = odbc_errormsg();
                    throw new Exception("Échec de la connexion ODBC: " . $error);
                }

                echo "<div class='success'>✅ <strong>Connexion ODBC réussie!</strong></div>";
                echo "<div class='info'>Temps de connexion: {$connection_time}ms</div>";

                // Test d'une requête simple
                try {
                    $result = odbc_exec($connection, "SELECT 1 as test");
                    if ($result) {
                        echo "<div class='success'>✅ Test de requête ODBC réussi</div>";
                        odbc_free_result($result);
                    }
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ Erreur lors du test de requête ODBC: " . $e->getMessage() . "</div>";
                }

                odbc_close($connection);

            } catch (Exception $e) {
                echo "<div class='error'>❌ <strong>Erreur de connexion ODBC:</strong><br>" . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error'>❌ L'extension ODBC n'est pas disponible</div>";
        }
        echo "</div>";

        // Informations système
        echo "<div class='test-section'>";
        echo "<h2>ℹ️ Informations Système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP:</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture:</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>OS:</strong> " . PHP_OS . "</li>";
        echo "<li><strong>SAPI:</strong> " . php_sapi_name() . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>
    </div>
</body>
</html>
