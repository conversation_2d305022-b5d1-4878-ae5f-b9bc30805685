<?php
/**
 * HFSQLManager - Classe basée sur votre solution PDO qui fonctionne
 * Accès temps réel aux données HFSQL via DSN système
 */

class HFSQLManager {
    private $pdo;
    private $dsn;
    private $username;
    private $password;
    private $connected = false;
    
    public function __construct($dsn = "odbc:DataCafe", $username = "admin", $password = "") {
        $this->dsn = $dsn;
        $this->username = $username;
        $this->password = $password;
        $this->connect();
    }
    
    /**
     * Connexion à HFSQL (basée sur votre solution qui fonctionne)
     */
    private function connect() {
        try {
            $this->pdo = new PDO($this->dsn, $this->username, $this->password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 30
            ]);
            $this->connected = true;
        } catch (PDOException $e) {
            $this->connected = false;
            throw new Exception("Erreur connexion HFSQL: " . $e->getMessage());
        }
    }
    
    /**
     * Vérifier si la connexion est active
     */
    public function isConnected() {
        return $this->connected;
    }
    
    /**
     * Équivalent de "POUR TOUT Categorie"
     * Récupère toutes les catégories
     */
    public function getCategories($orderBy = 'IDCategorie') {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $sql = "SELECT IDCategorie, categories, photo FROM Categorie ORDER BY " . $orderBy;
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    /**
     * Récupérer une catégorie spécifique (équivalent de HLitPremier)
     */
    public function getCategorieById($id) {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $sql = "SELECT IDCategorie, categories, photo FROM Categorie WHERE IDCategorie = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch();
    }
    
    /**
     * Équivalent de "POUR TOUT articles"
     * Récupère tous les articles avec possibilité de filtrage
     */
    public function getArticles($categorieId = null, $search = null, $orderBy = 'designation') {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $sql = "SELECT a.IDarticles, a.designation, a.IDCategorie, a.quantite, c.categories as nom_categorie 
                FROM articles a 
                LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie";
        
        $where_conditions = [];
        $params = [];
        
        if ($categorieId !== null) {
            $where_conditions[] = "a.IDCategorie = ?";
            $params[] = $categorieId;
        }
        
        if ($search !== null) {
            $where_conditions[] = "a.designation LIKE ?";
            $params[] = '%' . $search . '%';
        }
        
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(" AND ", $where_conditions);
        }
        
        $sql .= " ORDER BY " . $orderBy;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Récupérer un article spécifique
     */
    public function getArticleById($id) {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $sql = "SELECT a.IDarticles, a.designation, a.IDCategorie, a.quantite, c.categories as nom_categorie 
                FROM articles a 
                LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie 
                WHERE a.IDarticles = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch();
    }
    
    /**
     * Équivalent de ZoneRépétéeAjouteLigne pour les catégories
     * Génère le HTML pour une zone répétée de catégories
     */
    public function renderCategories($categories = null, $cssClass = 'zone-repetee') {
        if ($categories === null) {
            $categories = $this->getCategories();
        }
        
        $html = "<div class='$cssClass'>";
        
        foreach ($categories as $categorie) {
            $html .= "<div class='ligne-categorie' data-id='{$categorie['IDCategorie']}'>";
            
            // Photo
            if (!empty($categorie['photo']) && file_exists($categorie['photo'])) {
                $html .= "<img src='" . htmlspecialchars($categorie['photo']) . "' alt='" . htmlspecialchars($categorie['categories']) . "' class='photo-categorie'>";
            } else {
                $html .= "<div class='no-photo'>📂</div>";
            }
            
            // Nom
            $html .= "<div class='nom-categorie'>" . htmlspecialchars($categorie['categories']) . "</div>";
            
            // ID
            $html .= "<div class='id-categorie'>ID: {$categorie['IDCategorie']}</div>";
            
            $html .= "</div>";
        }
        
        $html .= "</div>";
        
        return $html;
    }
    
    /**
     * Équivalent de ZoneRépétéeAjouteLigne pour les articles
     */
    public function renderArticles($articles = null, $cssClass = 'zone-repetee') {
        if ($articles === null) {
            $articles = $this->getArticles();
        }
        
        $html = "<div class='$cssClass'>";
        
        foreach ($articles as $article) {
            $html .= "<div class='ligne-article' data-id='{$article['IDarticles']}'>";
            
            // Nom
            $html .= "<div class='nom-article'>" . htmlspecialchars($article['designation']) . "</div>";
            
            // Infos
            $html .= "<div class='info-article'>ID: {$article['IDarticles']}</div>";
            $html .= "<div class='info-article'>Catégorie: " . htmlspecialchars($article['nom_categorie'] ?? 'Non définie') . "</div>";
            $html .= "<div class='info-article'>Quantité: <span class='quantite'>{$article['quantite']}</span></div>";
            
            $html .= "</div>";
        }
        
        $html .= "</div>";
        
        return $html;
    }
    
    /**
     * Statistiques générales
     */
    public function getStats() {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $stats = [];
        
        // Nombre de catégories
        $stmt = $this->pdo->query("SELECT COUNT(*) as nb FROM Categorie");
        $stats['categories'] = $stmt->fetch()['nb'];
        
        // Nombre d'articles
        $stmt = $this->pdo->query("SELECT COUNT(*) as nb FROM articles");
        $stats['articles'] = $stmt->fetch()['nb'];
        
        // Quantité totale
        $stmt = $this->pdo->query("SELECT SUM(quantite) as total FROM articles");
        $stats['quantite_totale'] = $stmt->fetch()['total'] ?? 0;
        
        // Articles par catégorie
        $stmt = $this->pdo->query("SELECT c.categories, COUNT(a.IDarticles) as nb_articles 
                                   FROM Categorie c 
                                   LEFT JOIN articles a ON c.IDCategorie = a.IDCategorie 
                                   GROUP BY c.IDCategorie, c.categories 
                                   ORDER BY nb_articles DESC");
        $stats['articles_par_categorie'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Exécuter une requête SQL personnalisée
     */
    public function query($sql, $params = []) {
        if (!$this->connected) {
            throw new Exception("Pas de connexion HFSQL");
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Fermer la connexion
     */
    public function close() {
        $this->pdo = null;
        $this->connected = false;
    }
}

// === EXEMPLE D'UTILISATION ===

if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Ce code s'exécute seulement si le fichier est appelé directement
    
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Test HFSQLManager</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .zone-repetee { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px; }
            .ligne-categorie, .ligne-article { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
            .nom-categorie, .nom-article { font-weight: bold; margin-bottom: 10px; }
            .no-photo { width: 100%; height: 120px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; }
            .quantite { background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; }
            .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>";
    
    try {
        echo "<h1>Test HFSQLManager - Basé sur votre solution qui fonctionne</h1>";
        
        // Initialisation avec votre configuration qui marche
        $hfsql = new HFSQLManager("odbc:DataCafe", "admin", "");
        
        if ($hfsql->isConnected()) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>✅ Connexion HFSQL réussie !</div>";
            
            // Statistiques
            echo "<h2>📊 Statistiques</h2>";
            $stats = $hfsql->getStats();
            echo "<div class='stats'>";
            echo "<p><strong>Catégories :</strong> {$stats['categories']}</p>";
            echo "<p><strong>Articles :</strong> {$stats['articles']}</p>";
            echo "<p><strong>Quantité totale :</strong> {$stats['quantite_totale']}</p>";
            echo "</div>";
            
            // Test des catégories
            echo "<h2>📂 Test des catégories (POUR TOUT Categorie)</h2>";
            echo $hfsql->renderCategories();
            
            // Test des articles
            echo "<h2>📦 Test des articles (POUR TOUT articles)</h2>";
            $articles = $hfsql->getArticles(null, null, 'designation');
            echo "<p>Nombre d'articles : " . count($articles) . "</p>";
            echo $hfsql->renderArticles(array_slice($articles, 0, 6)); // Afficher seulement les 6 premiers
            
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>❌ Connexion HFSQL échouée</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>Erreur : " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</body></html>";
}
?>

<!--
UTILISATION DANS VOS PROJETS :

1. Inclusion simple :
   <?php
   require_once 'HFSQLManager.php';
   
   $hfsql = new HFSQLManager("odbc:DataCafe", "admin", "");
   
   // Équivalent de POUR TOUT Categorie
   $categories = $hfsql->getCategories();
   foreach ($categories as $cat) {
       echo $cat['categories'] . "<br>";
   }
   
   // Ou directement le HTML
   echo $hfsql->renderCategories();
   ?>

2. Avec filtres :
   <?php
   // Articles d'une catégorie spécifique
   $articles = $hfsql->getArticles(1); // Catégorie ID 1
   
   // Recherche d'articles
   $articles = $hfsql->getArticles(null, 'café');
   ?>

3. Requêtes personnalisées :
   <?php
   $results = $hfsql->query("SELECT * FROM Categorie WHERE categories LIKE ?", ['%boisson%']);
   ?>
-->
