<?php
// Configuration
$dsn = "odbc:DataCafe";
$username = "admin";
$password = "";

try {
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Équivalent du POUR TOUT Categorie
    $sql = "SELECT categories, photo, IDCategorie FROM Categorie";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    // Récupération de tous les résultats
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Génération du HTML (équivalent de la zone répétée)
    echo "<div class='zone-repetee'>";
    
    foreach ($categories as $categorie) {
        echo "<div class='ligne-categorie'>";
        echo "<span class='nom'>" . htmlspecialchars($categorie['categories']) . "</span>";
        echo "<img src='" . htmlspecialchars($categorie['photo']) . "' alt='Photo catégorie'>";
        echo "<span class='id' data-id='" . $categorie['IDCategorie'] . "'>" . $categorie['IDCategorie'] . "</span>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "Erreur : " . $e->getMessage();
}
?>