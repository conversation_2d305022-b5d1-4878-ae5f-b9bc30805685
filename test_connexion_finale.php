<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connexion Finale HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .critical { color: #721c24; background-color: #f5c6cb; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 5px solid #dc3545; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f8f9fa; }
        .dsn-code { font-family: monospace; font-size: 10px; max-width: 400px; word-break: break-all; }
        .result-success { background-color: #d4edda; color: #155724; }
        .result-error { background-color: #f8d7da; color: #721c24; }
        .solution { background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 5px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Test Connexion Finale HFSQL</h1>

        <div class="info">
            <h2>📋 Situation actuelle</h2>
            <ul>
                <li>✅ <strong>Serveur HFSQL :</strong> Fonctionne (tables visibles)</li>
                <li>✅ <strong>Base de données :</strong> "DataCafe" existe avec des tables</li>
                <li>✅ <strong>Driver ODBC :</strong> "HFSQL" reconnu</li>
                <li>❌ <strong>Connexion ODBC :</strong> "Chaîne de connexion insuffisante"</li>
            </ul>
        </div>

        <?php
        // Configuration basée sur les tables visibles
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Test 1: Test avec les noms de tables réels
        echo "<div class='test-section'>";
        echo "<h2>🧪 1. Test avec noms de tables réels</h2>";
        
        $table_names = ['Imprimantes', 'Categorie', 'articles', 'Unites', 'Serveur', 'wifi', 'tickets', 'VieJour', 'ModePye', 'Attente', 'AttenteLigne', 'Cuisines'];
        
        echo "<div class='info'>Tables détectées dans votre base : " . implode(', ', $table_names) . "</div>";
        
        // Test de connexion avec spécification de table
        $dsn_with_table = "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};DefaultTable=Imprimantes;UID={$config['username']};PWD={$config['password']}";
        
        echo "<h3>Test avec table par défaut</h3>";
        echo "<div class='dsn-code'>DSN: " . htmlspecialchars($dsn_with_table) . "</div>";
        
        $connection = @odbc_connect($dsn_with_table, $config['username'], $config['password']);
        if ($connection) {
            echo "<div class='success'>✅ Connexion réussie avec table par défaut !</div>";
            odbc_close($connection);
        } else {
            $error = odbc_errormsg();
            echo "<div class='error'>❌ Échec : " . htmlspecialchars($error) . "</div>";
        }
        echo "</div>";

        // Test 2: Test avec différents paramètres d'authentification
        echo "<div class='test-section'>";
        echo "<h2>🔐 2. Test avec différents paramètres d'authentification</h2>";
        
        $auth_variants = [
            'Sans authentification' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}",
            'Utilisateur vide' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID=;PWD=",
            'Utilisateur admin' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID=admin;PWD=",
            'Utilisateur root' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID=root;PWD=",
            'Utilisateur sa' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID=sa;PWD=",
            'Trusted Connection' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Trusted_Connection=yes",
            'Integrated Security' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Integrated Security=SSPI"
        ];
        
        echo "<table>";
        echo "<tr><th>Type d'authentification</th><th>DSN</th><th>Résultat</th><th>Erreur</th></tr>";
        
        foreach ($auth_variants as $name => $dsn) {
            echo "<tr>";
            echo "<td><strong>$name</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($dsn) . "</td>";
            
            $connection = @odbc_connect($dsn, '', '');
            if ($connection) {
                echo "<td class='result-success'>✅ Succès</td>";
                echo "<td>-</td>";
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                echo "<td class='result-error'>❌ Échec</td>";
                echo "<td style='font-size: 10px;'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";

        // Test 3: Test avec différents formats de base de données
        echo "<div class='test-section'>";
        echo "<h2>💾 3. Test avec différents formats de base de données</h2>";
        
        $db_variants = [
            'Database=DataCafe' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database=DataCafe;UID={$config['username']};PWD={$config['password']}",
            'Initial Catalog=DataCafe' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Initial Catalog=DataCafe;UID={$config['username']};PWD={$config['password']}",
            'DBQ=DataCafe' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};DBQ=DataCafe;UID={$config['username']};PWD={$config['password']}",
            'Sans base spécifiée' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}",
            'Avec chemin complet' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database=C:\\DataCafe;UID={$config['username']};PWD={$config['password']}",
            'Avec extension .fdb' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database=DataCafe.fdb;UID={$config['username']};PWD={$config['password']}"
        ];
        
        echo "<table>";
        echo "<tr><th>Format base</th><th>DSN</th><th>Résultat</th><th>Erreur</th></tr>";
        
        foreach ($db_variants as $name => $dsn) {
            echo "<tr>";
            echo "<td><strong>$name</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($dsn) . "</td>";
            
            $connection = @odbc_connect($dsn, $config['username'], $config['password']);
            if ($connection) {
                echo "<td class='result-success'>✅ Succès</td>";
                echo "<td>-</td>";
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                echo "<td class='result-error'>❌ Échec</td>";
                echo "<td style='font-size: 10px;'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";

        // Test 4: Test avec options spéciales HFSQL
        echo "<div class='test-section'>";
        echo "<h2>⚙️ 4. Test avec options spéciales HFSQL</h2>";
        
        $special_variants = [
            'Mode Client/Server' => "Driver={HFSQL};Mode=ClientServer;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}",
            'Protocol TCP' => "Driver={HFSQL};Protocol=TCP;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}",
            'Connection Type' => "Driver={HFSQL};ConnectionType=TCPIP;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}",
            'Avec Charset' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Charset=UTF8;UID={$config['username']};PWD={$config['password']}",
            'Avec Compression' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Compression=1;UID={$config['username']};PWD={$config['password']}",
            'Avec Encryption' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Encryption=0;UID={$config['username']};PWD={$config['password']}"
        ];
        
        echo "<table>";
        echo "<tr><th>Option spéciale</th><th>DSN</th><th>Résultat</th><th>Erreur</th></tr>";
        
        foreach ($special_variants as $name => $dsn) {
            echo "<tr>";
            echo "<td><strong>$name</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($dsn) . "</td>";
            
            $connection = @odbc_connect($dsn, $config['username'], $config['password']);
            if ($connection) {
                echo "<td class='result-success'>✅ Succès</td>";
                echo "<td>-</td>";
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                echo "<td class='result-error'>❌ Échec</td>";
                echo "<td style='font-size: 10px;'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";

        // Test 5: Recommandations finales
        echo "<div class='test-section'>";
        echo "<h2>💡 5. Recommandations finales</h2>";
        
        echo "<div class='critical'>";
        echo "<h3>🚨 Problème identifié</h3>";
        echo "<p>Le serveur HFSQL fonctionne parfaitement (tables visibles), mais <strong>l'accès ODBC n'est pas configuré correctement</strong>.</p>";
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h3>🔧 Solutions à essayer</h3>";
        echo "<ol>";
        echo "<li><strong>Vérifiez la version du driver HFSQL ODBC :</strong>";
        echo "<ul>";
        echo "<li>Le driver doit être compatible avec votre version de serveur HFSQL</li>";
        echo "<li>Téléchargez la dernière version depuis PC SOFT</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Configurez l'accès ODBC dans HFSQL :</strong>";
        echo "<ul>";
        echo "<li>Dans le Centre de Contrôle HFSQL, allez dans Configuration</li>";
        echo "<li>Activez explicitement l'accès ODBC/OLE DB</li>";
        echo "<li>Configurez les permissions pour l'utilisateur 'admin'</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Essayez un DSN système :</strong>";
        echo "<ul>";
        echo "<li>Créez un DSN système via 'Sources de données ODBC'</li>";
        echo "<li>Testez la connexion dans l'interface Windows</li>";
        echo "<li>Si ça fonctionne dans Windows, ça devrait fonctionner en PHP</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Vérifiez les logs HFSQL :</strong>";
        echo "<ul>";
        echo "<li>Consultez les logs du serveur HFSQL</li>";
        echo "<li>Cherchez des erreurs liées aux tentatives de connexion ODBC</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Contactez le support PC SOFT :</strong>";
        echo "<ul>";
        echo "<li>Avec les informations de cette page</li>";
        echo "<li>Mentionnez l'erreur 'S1000 - chaîne de connexion insuffisante'</li>";
        echo "<li>Précisez que le serveur fonctionne mais pas l'accès ODBC</li>";
        echo "</ul></li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";

        // Informations pour le support
        echo "<div class='test-section'>";
        echo "<h2>📋 Informations pour le support</h2>";
        echo "<div class='info'>";
        echo "<ul>";
        echo "<li><strong>Erreur :</strong> [S1000] Impossible de se connecter : la chaîne de connexion est insuffisant</li>";
        echo "<li><strong>Driver ODBC :</strong> HFSQL (reconnu par Windows)</li>";
        echo "<li><strong>Serveur HFSQL :</strong> Fonctionne (tables accessibles via interface)</li>";
        echo "<li><strong>Base de données :</strong> DataCafe (contient des tables)</li>";
        echo "<li><strong>Port :</strong> 4900 (accessible via socket)</li>";
        echo "<li><strong>PHP :</strong> " . PHP_VERSION . " (" . (PHP_INT_SIZE * 8) . " bits)</li>";
        echo "<li><strong>OS :</strong> " . PHP_OS . "</li>";
        echo "<li><strong>Toutes les variantes de DSN :</strong> Même erreur</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer le test</a>
                <a href="test_dsn_manuel.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔧 Test DSN manuel</a>
            </p>
        </div>
    </div>
</body>
</html>
