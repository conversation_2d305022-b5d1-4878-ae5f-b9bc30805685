<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Complet HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic Complet HFSQL</h1>

        <?php
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Test 1: Vérification des processus HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔍 1. Processus HFSQL en cours d'exécution</h2>";
        
        $processes = shell_exec('tasklist /FI "IMAGENAME eq manta*" /FO CSV 2>nul');
        if (!$processes) {
            $processes = shell_exec('tasklist /FI "IMAGENAME eq hf*" /FO CSV 2>nul');
        }
        if (!$processes) {
            $processes = shell_exec('tasklist /FI "IMAGENAME eq windev*" /FO CSV 2>nul');
        }
        
        if ($processes && strlen(trim($processes)) > 0) {
            echo "<div class='success'>✅ Processus HFSQL détectés:</div>";
            echo "<pre>" . htmlspecialchars($processes) . "</pre>";
        } else {
            echo "<div class='error'>❌ Aucun processus HFSQL détecté</div>";
            echo "<div class='warning'>Recherchez manuellement les processus contenant 'hf', 'manta', ou 'windev'</div>";
        }
        echo "</div>";

        // Test 2: Test de connectivité réseau détaillé
        echo "<div class='test-section'>";
        echo "<h2>🌐 2. Test de connectivité réseau</h2>";
        
        // Test ping
        echo "<h3>Ping vers {$config['server']}</h3>";
        $ping_result = shell_exec("ping -n 2 {$config['server']} 2>&1");
        if (strpos($ping_result, 'TTL=') !== false) {
            echo "<div class='success'>✅ Ping réussi</div>";
        } else {
            echo "<div class='error'>❌ Ping échoué</div>";
            echo "<pre>" . htmlspecialchars($ping_result) . "</pre>";
        }
        
        // Test telnet/netcat sur le port
        echo "<h3>Test de connectivité sur le port {$config['port']}</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($socket) {
            fclose($socket);
            echo "<div class='success'>✅ Port {$config['port']} accessible</div>";
        } else {
            echo "<div class='error'>❌ Port {$config['port']} inaccessible</div>";
            echo "<div class='warning'>Erreur: $errstr ($errno)</div>";
            
            // Test avec netstat pour voir quels ports sont ouverts
            echo "<h4>Ports ouverts sur le système:</h4>";
            $netstat = shell_exec("netstat -an | findstr :4900");
            if ($netstat) {
                echo "<pre>" . htmlspecialchars($netstat) . "</pre>";
            } else {
                echo "<div class='warning'>Aucun processus n'écoute sur le port 4900</div>";
                
                // Chercher d'autres ports HFSQL courants
                echo "<h4>Recherche d'autres ports HFSQL courants:</h4>";
                $common_ports = [4900, 4901, 4902, 4999, 5000, 8080, 8000];
                foreach ($common_ports as $port) {
                    $test_socket = @fsockopen($config['server'], $port, $errno, $errstr, 1);
                    if ($test_socket) {
                        fclose($test_socket);
                        echo "<div class='info'>✅ Port $port est ouvert</div>";
                    }
                }
            }
        }
        echo "</div>";

        // Test 3: Vérification des drivers ODBC détaillée
        echo "<div class='test-section'>";
        echo "<h2>🔧 3. Drivers ODBC installés</h2>";
        
        // Liste tous les drivers ODBC
        $odbc_drivers = shell_exec('powershell -Command "Get-OdbcDriver | Format-Table Name, Platform, Version -AutoSize" 2>nul');
        if ($odbc_drivers) {
            echo "<div class='info'>Drivers ODBC disponibles:</div>";
            echo "<pre>" . htmlspecialchars($odbc_drivers) . "</pre>";
            
            // Vérifier spécifiquement HFSQL
            if (strpos($odbc_drivers, 'HFSQL') !== false) {
                echo "<div class='success'>✅ Driver HFSQL trouvé</div>";
            } else {
                echo "<div class='error'>❌ Driver HFSQL non trouvé</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ Impossible de lister les drivers ODBC via PowerShell</div>";
        }
        
        // Vérification via le registre
        echo "<h3>Vérification via le registre Windows</h3>";
        $reg_result = shell_exec('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\ODBC\ODBCINST.INI" /s 2>nul | findstr /i hfsql');
        if ($reg_result) {
            echo "<div class='success'>✅ Entrées HFSQL trouvées dans le registre:</div>";
            echo "<pre>" . htmlspecialchars($reg_result) . "</pre>";
        } else {
            echo "<div class='error'>❌ Aucune entrée HFSQL dans le registre ODBC</div>";
        }
        echo "</div>";

        // Test 4: Vérification des DSN système
        echo "<div class='test-section'>";
        echo "<h2>📋 4. DSN système configurés</h2>";
        
        $system_dsn = shell_exec('powershell -Command "Get-OdbcDsn -DsnType System | Format-Table Name, DriverName, Platform -AutoSize" 2>nul');
        if ($system_dsn) {
            echo "<div class='info'>DSN système disponibles:</div>";
            echo "<pre>" . htmlspecialchars($system_dsn) . "</pre>";
            
            if (strpos($system_dsn, 'HFSQL') !== false || strpos($system_dsn, 'DataCafe') !== false) {
                echo "<div class='success'>✅ DSN HFSQL ou DataCafe trouvé</div>";
            } else {
                echo "<div class='warning'>⚠️ Aucun DSN HFSQL configuré</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ Impossible de lister les DSN système</div>";
        }
        echo "</div>";

        // Test 5: Test de connexion avec différents drivers
        echo "<div class='test-section'>";
        echo "<h2>🔌 5. Test avec différents noms de drivers</h2>";
        
        if (extension_loaded('odbc')) {
            $driver_variants = [
                'HFSQL',
                'HFSQL ODBC Driver',
                'HFSQL ODBC',
                'PC SOFT HFSQL',
                'WinDev HFSQL',
                'HFSQL Client/Server',
                'HFSQL Classic',
                'HFSQL Network'
            ];
            
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr><th style='border: 1px solid #ddd; padding: 8px;'>Driver</th><th style='border: 1px solid #ddd; padding: 8px;'>Résultat</th><th style='border: 1px solid #ddd; padding: 8px;'>Erreur</th></tr>";
            
            foreach ($driver_variants as $driver) {
                $test_dsn = "Driver={$driver};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";
                
                echo "<tr>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>$driver</code></td>";
                
                $connection = @odbc_connect($test_dsn, $config['username'], $config['password']);
                if ($connection) {
                    echo "<td style='border: 1px solid #ddd; padding: 8px; background-color: #d4edda;'>✅ Succès</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>-</td>";
                    odbc_close($connection);
                } else {
                    $error = odbc_errormsg();
                    echo "<td style='border: 1px solid #ddd; padding: 8px; background-color: #f8d7da;'>❌ Échec</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px; font-size: 12px;'>" . htmlspecialchars(substr($error, 0, 100)) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ Extension ODBC non chargée</div>";
        }
        echo "</div>";

        // Test 6: Informations sur l'installation HFSQL
        echo "<div class='test-section'>";
        echo "<h2>📦 6. Recherche d'installation HFSQL/WinDev</h2>";
        
        $common_paths = [
            'C:\Program Files\PC SOFT',
            'C:\Program Files (x86)\PC SOFT',
            'C:\WinDev',
            'C:\HFSQL',
            'C:\Program Files\WinDev',
            'C:\Program Files (x86)\WinDev'
        ];
        
        foreach ($common_paths as $path) {
            if (is_dir($path)) {
                echo "<div class='success'>✅ Trouvé: $path</div>";
                $files = glob($path . '\*', GLOB_ONLYDIR);
                if ($files) {
                    echo "<div class='info'>Sous-dossiers: " . implode(', ', array_map('basename', array_slice($files, 0, 5))) . "</div>";
                }
            }
        }
        echo "</div>";

        // Recommandations
        echo "<div class='test-section'>";
        echo "<h2>💡 7. Recommandations</h2>";
        echo "<div class='info'>";
        echo "<h3>Actions à effectuer:</h3>";
        echo "<ol>";
        echo "<li><strong>Vérifiez le serveur HFSQL:</strong> Assurez-vous qu'un serveur HFSQL est démarré et écoute sur le port 4900</li>";
        echo "<li><strong>Installez le driver ODBC HFSQL:</strong> Si aucun driver n'est trouvé, installez-le depuis PC SOFT</li>";
        echo "<li><strong>Configurez un DSN système:</strong> Créez un DSN système dans Windows pour tester la connexion</li>";
        echo "<li><strong>Vérifiez les paramètres de connexion:</strong> Confirmez le nom de la base, l'utilisateur et le mot de passe</li>";
        echo "<li><strong>Testez avec l'outil HFSQL:</strong> Utilisez l'outil d'administration HFSQL pour vérifier la connectivité</li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";

        // Informations système
        echo "<div class='test-section'>";
        echo "<h2>ℹ️ Informations système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP:</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture:</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>OS:</strong> " . PHP_OS . "</li>";
        echo "<li><strong>Extensions chargées:</strong> " . (extension_loaded('odbc') ? 'ODBC ✅' : 'ODBC ❌') . ", " . (extension_loaded('pdo_odbc') ? 'PDO_ODBC ✅' : 'PDO_ODBC ❌') . "</li>";
        echo "<li><strong>Date/Heure:</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔄 Relancer le diagnostic</a></p>
        </div>
    </div>
</body>
</html>
