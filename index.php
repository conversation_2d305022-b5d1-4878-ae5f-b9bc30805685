<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connexion HFSQL WinDev</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-section { margin: 20px 0; border: 1px solid #ddd; padding: 15px; border-radius: 4px; }
        h1, h2 { color: #333; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test de Connexion HFSQL WinDev</h1>

        <?php
        // Configuration HFSQL
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => '',
            'driver' => 'HFSQL ODBC Driver'
        ];

        echo "<div class='test-section'>";
        echo "<h2>📋 Configuration</h2>";
        echo "<ul>";
        echo "<li><strong>Serveur:</strong> {$config['server']}</li>";
        echo "<li><strong>Port:</strong> {$config['port']}</li>";
        echo "<li><strong>Base de données:</strong> {$config['database']}</li>";
        echo "<li><strong>Utilisateur:</strong> {$config['username']}</li>";
        echo "<li><strong>Driver:</strong> {$config['driver']}</li>";
        echo "</ul>";
        echo "</div>";

        // Test 1: Vérification de l'extension ODBC
        echo "<div class='test-section'>";
        echo "<h2>🔧 Prérequis PHP</h2>";

        if (extension_loaded('odbc')) {
            echo "<div class='success'>✅ Extension ODBC chargée (Version: " . phpversion('odbc') . ")</div>";
        } else {
            echo "<div class='error'>❌ Extension ODBC non disponible</div>";
            echo "<div class='warning'>⚠️ Vous devez installer l'extension PHP ODBC pour continuer.</div>";
            echo "</div></div></body></html>";
            exit;
        }

        echo "<div class='info'>ℹ️ Version PHP: " . phpversion() . "</div>";
        echo "</div>";

        // Test 2: Test de connectivité réseau
        echo "<div class='test-section'>";
        echo "<h2>🌐 Test de Connectivité</h2>";

        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($socket) {
            echo "<div class='success'>✅ Port {$config['port']} accessible sur {$config['server']}</div>";
            fclose($socket);
        } else {
            echo "<div class='error'>❌ Impossible de se connecter à {$config['server']}:{$config['port']}</div>";
            echo "<div class='warning'>Erreur: $errstr ($errno)</div>";
        }
        echo "</div>";

        // Test 3: Test de connexion HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔌 Test de Connexion HFSQL</h2>";

        $dsn = "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";

        echo "<div class='info'><strong>DSN utilisé:</strong><br><code>$dsn</code></div>";

        try {
            $start_time = microtime(true);
            $connection = odbc_connect($dsn, $config['username'], $config['password']);
            $connection_time = round((microtime(true) - $start_time) * 1000, 2);

            if (!$connection) {
                $error = odbc_errormsg();
                throw new Exception("Échec de la connexion ODBC: " . $error);
            }

            echo "<div class='success'>✅ <strong>Connexion réussie !</strong> (Temps: {$connection_time}ms)</div>";

            // Test d'une requête simple
            echo "<h3>📊 Test de Requête</h3>";

            $test_queries = [
                "SELECT 1 AS test_value" => "Test basique",
                "SELECT CURRENT_TIMESTAMP AS current_time" => "Test fonction système"
            ];

            foreach ($test_queries as $query => $description) {
                try {
                    $result = odbc_exec($connection, $query);
                    if ($result) {
                        echo "<div class='success'>✅ $description: OK</div>";

                        if (odbc_fetch_row($result)) {
                            $value = odbc_result($result, 1);
                            echo "<div class='info'>Résultat: $value</div>";
                        }
                    } else {
                        $error = odbc_errormsg($connection);
                        echo "<div class='warning'>⚠️ $description: $error</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ $description: " . $e->getMessage() . "</div>";
                }
            }

            // Essayer de lister les tables
            echo "<h3>📋 Tables Disponibles</h3>";
            try {
                $tables_query = "SHOW TABLES";
                $result = odbc_exec($connection, $tables_query);

                if ($result) {
                    echo "<div class='success'>✅ Requête SHOW TABLES exécutée</div>";
                    echo "<ul>";
                    $table_count = 0;
                    while (odbc_fetch_row($result) && $table_count < 20) {
                        $table_name = odbc_result($result, 1);
                        echo "<li>$table_name</li>";
                        $table_count++;
                    }
                    echo "</ul>";

                    if ($table_count == 0) {
                        echo "<div class='warning'>⚠️ Aucune table trouvée ou accès restreint</div>";
                    } else {
                        echo "<div class='info'>Nombre de tables affichées: $table_count</div>";
                    }
                } else {
                    $error = odbc_errormsg($connection);
                    echo "<div class='warning'>⚠️ Impossible de lister les tables: $error</div>";
                    echo "<div class='info'>Cela peut être normal selon la configuration de sécurité HFSQL</div>";
                }
            } catch (Exception $e) {
                echo "<div class='warning'>⚠️ Erreur lors de la liste des tables: " . $e->getMessage() . "</div>";
            }

            odbc_close($connection);
            echo "<div class='success'>✅ Connexion fermée proprement</div>";

        } catch (Exception $e) {
            echo "<div class='error'>❌ <strong>Erreur de connexion:</strong><br>" . $e->getMessage() . "</div>";

            echo "<h3>🔧 Suggestions de dépannage:</h3>";
            echo "<ul>";
            echo "<li>Vérifiez que le serveur HFSQL WinDev est démarré</li>";
            echo "<li>Vérifiez l'adresse IP et le port du serveur (actuellement: {$config['server']}:{$config['port']})</li>";
            echo "<li>Vérifiez que le driver HFSQL ODBC est installé sur ce serveur web</li>";
            echo "<li>Vérifiez les identifiants de connexion (utilisateur: {$config['username']})</li>";
            echo "<li>Vérifiez que la base de données '{$config['database']}' existe</li>";
            echo "<li>Vérifiez les permissions de l'utilisateur sur la base</li>";
            echo "<li>Vérifiez le pare-feu (port {$config['port']} doit être ouvert)</li>";
            echo "</ul>";
        }

        echo "</div>";

        // Informations système
        echo "<div class='test-section'>";
        echo "<h2>ℹ️ Informations Système</h2>";
        echo "<ul>";
        echo "<li><strong>Système:</strong> " . php_uname() . "</li>";
        echo "<li><strong>Date/Heure:</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "<li><strong>Serveur Web:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Non défini') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div class="test-section">
            <h2>🔄 Actions</h2>
            <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔄 Relancer le test</a></p>
        </div>
    </div>
</body>
</html>