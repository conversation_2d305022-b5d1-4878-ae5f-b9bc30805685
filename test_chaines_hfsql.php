<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chaînes de Connexion HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .dsn-code { font-family: monospace; font-size: 11px; max-width: 500px; word-break: break-all; }
        .result-success { background-color: #d4edda; color: #155724; }
        .result-error { background-color: #f8d7da; color: #721c24; }
        .result-warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Chaînes de Connexion HFSQL</h1>

        <?php
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        echo "<div class='info'>";
        echo "<h2>📋 Configuration de test</h2>";
        echo "<ul>";
        echo "<li><strong>Serveur:</strong> {$config['server']}</li>";
        echo "<li><strong>Port:</strong> {$config['port']}</li>";
        echo "<li><strong>Base de données:</strong> {$config['database']}</li>";
        echo "<li><strong>Utilisateur:</strong> {$config['username']}</li>";
        echo "</ul>";
        echo "</div>";

        // Test de connectivité réseau d'abord
        echo "<div class='info'>";
        echo "<h2>🌐 Test de connectivité réseau</h2>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($socket) {
            fclose($socket);
            echo "<div class='success'>✅ Port {$config['port']} accessible</div>";
        } else {
            echo "<div class='error'>❌ Port {$config['port']} inaccessible: $errstr ($errno)</div>";
            echo "<div class='warning'>⚠️ Le serveur HFSQL n'est peut-être pas démarré</div>";
        }
        echo "</div>";

        // Différentes variantes de chaînes de connexion HFSQL
        $connection_variants = [
            // Formats de base
            [
                'name' => 'Format minimal',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']}"
            ],
            [
                'name' => 'Avec base de données',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}"
            ],
            [
                'name' => 'Avec authentification',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Complet standard',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Formats alternatifs pour HFSQL
            [
                'name' => 'Format HFSQL spécifique 1',
                'dsn' => "Driver={HFSQL};ServerName={$config['server']};ServerPort={$config['port']};DatabaseName={$config['database']};UserName={$config['username']};Password={$config['password']}"
            ],
            [
                'name' => 'Format HFSQL spécifique 2',
                'dsn' => "Driver={HFSQL};HOST={$config['server']};PORT={$config['port']};DB={$config['database']};USER={$config['username']};PASS={$config['password']}"
            ],
            [
                'name' => 'Format avec Data Source',
                'dsn' => "Driver={HFSQL};Data Source={$config['server']};Port={$config['port']};Initial Catalog={$config['database']};User ID={$config['username']};Password={$config['password']}"
            ],
            
            // Formats sans certains paramètres
            [
                'name' => 'Sans base de données',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Sans authentification',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}"
            ],
            [
                'name' => 'Serveur et port seulement',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']}"
            ],
            
            // Formats avec options supplémentaires
            [
                'name' => 'Avec Timeout',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']};Timeout=30"
            ],
            [
                'name' => 'Avec Trusted Connection',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Trusted_Connection=yes"
            ],
            [
                'name' => 'Avec AutoCommit',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']};AutoCommit=1"
            ],
            
            // Formats avec différentes syntaxes
            [
                'name' => 'Format URL-like',
                'dsn' => "Driver={HFSQL};Server={$config['server']}:{$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Format avec espaces',
                'dsn' => "Driver = {HFSQL}; Server = {$config['server']}; Port = {$config['port']}; Database = {$config['database']}; UID = {$config['username']}; PWD = {$config['password']}"
            ],
            [
                'name' => 'Format sans accolades',
                'dsn' => "Driver=HFSQL;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Formats avec localhost
            [
                'name' => 'Localhost au lieu de 127.0.0.1',
                'dsn' => "Driver={HFSQL};Server=localhost;Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Formats sans port (port par défaut)
            [
                'name' => 'Sans port spécifié',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Formats spéciaux HFSQL
            [
                'name' => 'Format HFSQL Client/Server',
                'dsn' => "Driver={HFSQL};Mode=ClientServer;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Format avec Protocol',
                'dsn' => "Driver={HFSQL};Protocol=TCP;Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ]
        ];

        echo "<div class='info'>";
        echo "<h2>🧪 Test des variantes de chaînes de connexion</h2>";
        echo "<table>";
        echo "<tr><th style='width: 200px;'>Variante</th><th>Chaîne de connexion</th><th style='width: 100px;'>Résultat</th><th style='width: 80px;'>Temps (ms)</th><th>Détails</th></tr>";

        $successful_connections = [];
        $connection_details = [];

        foreach ($connection_variants as $variant) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($variant['name']) . "</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($variant['dsn']) . "</td>";

            // Test de connexion
            $start_time = microtime(true);
            $connection = @odbc_connect($variant['dsn'], $config['username'], $config['password']);
            $connection_time = round((microtime(true) - $start_time) * 1000, 2);

            if ($connection) {
                echo "<td class='result-success'>✅ Succès</td>";
                echo "<td>{$connection_time}</td>";
                
                // Test d'une requête simple
                $query_result = @odbc_exec($connection, "SELECT 1 AS test");
                if ($query_result) {
                    echo "<td class='result-success'>Connexion + Requête OK</td>";
                    $successful_connections[] = $variant;
                    $connection_details[] = "✅ Requête SELECT 1 réussie";
                } else {
                    echo "<td class='result-warning'>Connexion OK, requête échouée</td>";
                    $successful_connections[] = $variant;
                    $connection_details[] = "⚠️ Connexion OK mais requête échouée: " . odbc_errormsg($connection);
                }
                
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                echo "<td class='result-error'>❌ Échec</td>";
                echo "<td>{$connection_time}</td>";
                
                if (strpos($error, 'insuffisant') !== false) {
                    echo "<td class='result-warning'>Chaîne insuffisante</td>";
                } elseif (strpos($error, 'introuvable') !== false) {
                    echo "<td class='result-error'>Driver non trouvé</td>";
                } elseif (strpos($error, 'connexion') !== false) {
                    echo "<td class='result-warning'>Erreur de connexion</td>";
                } else {
                    echo "<td class='result-error'>" . htmlspecialchars(substr($error, 0, 30)) . "...</td>";
                }
            }
            echo "</tr>";
        }

        echo "</table>";
        echo "</div>";

        // Résumé des résultats
        if (!empty($successful_connections)) {
            echo "<div class='success'>";
            echo "<h2>🎉 Connexions réussies !</h2>";
            
            foreach ($successful_connections as $index => $connection) {
                echo "<h3>✅ " . htmlspecialchars($connection['name']) . "</h3>";
                echo "<div class='dsn-code' style='background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
                echo htmlspecialchars($connection['dsn']);
                echo "</div>";
                
                if (isset($connection_details[$index])) {
                    echo "<p><strong>Test:</strong> " . $connection_details[$index] . "</p>";
                }
            }
            
            echo "<h3>💻 Code à utiliser dans votre application:</h3>";
            $best_connection = $successful_connections[0];
            echo "<div class='dsn-code' style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>";
            echo htmlspecialchars('$dsn = "' . $best_connection['dsn'] . '";') . "<br>";
            echo htmlspecialchars('$connection = odbc_connect($dsn, "' . $config['username'] . '", "' . $config['password'] . '");');
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Aucune connexion réussie</h2>";
            echo "<p>Aucune des variantes de chaînes de connexion n'a fonctionné.</p>";
            echo "<h3>🔧 Vérifications nécessaires:</h3>";
            echo "<ul>";
            echo "<li><strong>Serveur HFSQL:</strong> Vérifiez qu'il est démarré et écoute sur le port {$config['port']}</li>";
            echo "<li><strong>Base de données:</strong> Vérifiez que '{$config['database']}' existe</li>";
            echo "<li><strong>Utilisateur:</strong> Vérifiez que '{$config['username']}' a les bonnes permissions</li>";
            echo "<li><strong>Pare-feu:</strong> Vérifiez que le port {$config['port']} est ouvert</li>";
            echo "<li><strong>Configuration HFSQL:</strong> Consultez la documentation PC SOFT pour la syntaxe exacte</li>";
            echo "</ul>";
            echo "</div>";
        }

        // Informations système
        echo "<div class='info'>";
        echo "<h2>ℹ️ Informations système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP:</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture:</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>Extension ODBC:</strong> " . (extension_loaded('odbc') ? 'Chargée ✅' : 'Non chargée ❌') . "</li>";
        echo "<li><strong>Date/Heure:</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer le test</a>
                <a href="diagnostic_complet.php" style="background-color: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔍 Diagnostic complet</a>
            </p>
        </div>
    </div>
</body>
</html>
