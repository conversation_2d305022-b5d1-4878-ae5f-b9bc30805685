<?php
/**
 * Script de vérification de connexion HFSQL WinDev
 * Ce script teste la connexion au serveur HFSQL et affiche des informations de diagnostic
 */

// Configuration de la connexion HFSQL
$config = [
    'server' => '127.0.0.1',
    'port' => '4900',
    'database' => 'DataCafe',
    'username' => 'admin',
    'password' => '',
    'driver' => 'HFSQL ODBC Driver'
];

echo "<h1>Test de Connexion HFSQL WinDev</h1>\n";
echo "<hr>\n";

// Fonction pour afficher les informations de configuration
function afficherConfiguration($config) {
    echo "<h2>Configuration de connexion :</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Serveur :</strong> {$config['server']}</li>\n";
    echo "<li><strong>Port :</strong> {$config['port']}</li>\n";
    echo "<li><strong>Base de données :</strong> {$config['database']}</li>\n";
    echo "<li><strong>Utilisateur :</strong> {$config['username']}</li>\n";
    echo "<li><strong>Driver :</strong> {$config['driver']}</li>\n";
    echo "</ul>\n";
}

// Fonction pour tester la connexion
function testerConnexion($config) {
    echo "<h2>Test de connexion :</h2>\n";
    
    // Construction du DSN
    $dsn = "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";
    
    echo "<p><strong>DSN utilisé :</strong> $dsn</p>\n";
    
    try {
        echo "<p>Tentative de connexion...</p>\n";
        
        // Test de connexion ODBC
        $connection = odbc_connect($dsn, $config['username'], $config['password']);
        
        if (!$connection) {
            $error = odbc_errormsg();
            throw new Exception("Échec de la connexion ODBC. Erreur : " . $error);
        }
        
        echo "<p style='color: green;'><strong>✓ Connexion réussie !</strong></p>\n";
        
        // Test d'une requête simple
        echo "<h3>Test d'une requête simple :</h3>\n";
        
        // Essayer de lister les tables
        $tables_query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
        $result = odbc_exec($connection, $tables_query);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Requête exécutée avec succès</p>\n";
            echo "<h4>Tables disponibles :</h4>\n";
            echo "<ul>\n";
            
            $table_count = 0;
            while (odbc_fetch_row($result)) {
                $table_name = odbc_result($result, 1);
                echo "<li>$table_name</li>\n";
                $table_count++;
            }
            
            if ($table_count == 0) {
                echo "<li><em>Aucune table trouvée ou accès limité</em></li>\n";
            }
            
            echo "</ul>\n";
            echo "<p>Nombre total de tables : $table_count</p>\n";
            
        } else {
            echo "<p style='color: orange;'>⚠ Impossible d'exécuter la requête de test des tables</p>\n";
            echo "<p>Erreur : " . odbc_errormsg() . "</p>\n";
        }
        
        // Informations sur la connexion
        echo "<h3>Informations sur la connexion :</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Version du driver :</strong> " . odbc_result_all($connection) . "</li>\n";
        echo "</ul>\n";
        
        // Fermer la connexion
        odbc_close($connection);
        echo "<p style='color: green;'>✓ Connexion fermée proprement</p>\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>✗ Erreur de connexion :</strong></p>\n";
        echo "<p style='color: red;'>" . $e->getMessage() . "</p>\n";
        
        // Suggestions de dépannage
        echo "<h3>Suggestions de dépannage :</h3>\n";
        echo "<ul>\n";
        echo "<li>Vérifiez que le serveur HFSQL est démarré</li>\n";
        echo "<li>Vérifiez l'adresse IP et le port du serveur</li>\n";
        echo "<li>Vérifiez que le driver HFSQL ODBC est installé</li>\n";
        echo "<li>Vérifiez les identifiants de connexion</li>\n";
        echo "<li>Vérifiez les permissions de la base de données</li>\n";
        echo "<li>Vérifiez le pare-feu (port 4900)</li>\n";
        echo "</ul>\n";
        
        return false;
    }
}

// Fonction pour vérifier les prérequis
function verifierPrerequis() {
    echo "<h2>Vérification des prérequis :</h2>\n";
    
    // Vérifier l'extension ODBC
    if (extension_loaded('odbc')) {
        echo "<p style='color: green;'>✓ Extension PHP ODBC chargée</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Extension PHP ODBC non disponible</p>\n";
        return false;
    }
    
    // Lister les drivers ODBC disponibles
    echo "<h3>Drivers ODBC disponibles :</h3>\n";
    echo "<ul>\n";
    
    // Cette fonction peut ne pas être disponible sur tous les systèmes
    if (function_exists('odbc_data_source')) {
        $ds = odbc_data_source(null, SQL_FETCH_FIRST);
        if ($ds) {
            do {
                echo "<li>{$ds['server']} - {$ds['description']}</li>\n";
            } while ($ds = odbc_data_source(null, SQL_FETCH_NEXT));
        } else {
            echo "<li><em>Impossible de lister les sources de données</em></li>\n";
        }
    } else {
        echo "<li><em>Fonction odbc_data_source non disponible</em></li>\n";
    }
    
    echo "</ul>\n";
    
    return true;
}

// Fonction pour tester la connectivité réseau
function testerConnectiviteReseau($server, $port) {
    echo "<h2>Test de connectivité réseau :</h2>\n";
    
    echo "<p>Test de connexion TCP vers $server:$port...</p>\n";
    
    $socket = @fsockopen($server, $port, $errno, $errstr, 5);
    
    if ($socket) {
        echo "<p style='color: green;'>✓ Port $port accessible sur $server</p>\n";
        fclose($socket);
        return true;
    } else {
        echo "<p style='color: red;'>✗ Impossible de se connecter à $server:$port</p>\n";
        echo "<p style='color: red;'>Erreur : $errstr ($errno)</p>\n";
        return false;
    }
}

// Exécution des tests
echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>\n";

afficherConfiguration($config);

if (verifierPrerequis()) {
    testerConnectiviteReseau($config['server'], $config['port']);
    testerConnexion($config);
} else {
    echo "<p style='color: red;'><strong>Impossible de continuer : prérequis non satisfaits</strong></p>\n";
}

echo "</div>\n";

echo "<hr>\n";
echo "<p><em>Test terminé à " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
