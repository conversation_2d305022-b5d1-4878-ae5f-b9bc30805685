<?php
echo "<h1>Vérification des Drivers ODBC</h1>";

echo "<h2>Extensions PHP chargées:</h2>";
$extensions = ['odbc', 'pdo', 'pdo_odbc'];
foreach ($extensions as $ext) {
    echo $ext . ": " . (extension_loaded($ext) ? "✅ Chargée" : "❌ Non chargée") . "<br>";
}

echo "<h2>Drivers ODBC disponibles (via PowerShell):</h2>";
echo "<pre>";
$output = shell_exec('powershell -Command "Get-OdbcDriver | Format-Table Name, Platform, Version -AutoSize"');
echo htmlspecialchars($output);
echo "</pre>";

echo "<h2>Test de connexion ODBC simple:</h2>";
if (function_exists('odbc_connect')) {
    echo "✅ Fonction odbc_connect disponible<br>";
    
    // Test avec le driver HFSQL
    $dsn = "Driver={HFSQL};Server=127.0.0.1;Port=4900;Database=DataCafe;UID=admin;PWD=";
    echo "Test DSN: " . htmlspecialchars($dsn) . "<br>";
    
    $conn = @odbc_connect($dsn, 'admin', '');
    if ($conn) {
        echo "✅ Connexion ODBC réussie!<br>";
        odbc_close($conn);
    } else {
        echo "❌ Erreur ODBC: " . odbc_errormsg() . "<br>";
    }
} else {
    echo "❌ Fonction odbc_connect non disponible<br>";
}

echo "<h2>Test PDO ODBC:</h2>";
if (class_exists('PDO') && in_array('odbc', PDO::getAvailableDrivers())) {
    echo "✅ PDO ODBC disponible<br>";
    
    $dsn = "odbc:Driver={HFSQL};Server=127.0.0.1;Port=4900;Database=DataCafe";
    echo "Test DSN PDO: " . htmlspecialchars($dsn) . "<br>";
    
    try {
        $pdo = new PDO($dsn, 'admin', '');
        echo "✅ Connexion PDO réussie!<br>";
    } catch (PDOException $e) {
        echo "❌ Erreur PDO: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ PDO ODBC non disponible<br>";
    if (class_exists('PDO')) {
        echo "Drivers PDO disponibles: " . implode(', ', PDO::getAvailableDrivers()) . "<br>";
    }
}

echo "<h2>Informations PHP:</h2>";
echo "Version PHP: " . PHP_VERSION . "<br>";
echo "Architecture: " . (PHP_INT_SIZE * 8) . " bits<br>";
echo "SAPI: " . php_sapi_name() . "<br>";
?>
