<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solutions Alternatives HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .critical { color: #721c24; background-color: #f5c6cb; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 5px solid #dc3545; }
        .solution { background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 5px solid #17a2b8; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .step { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Solutions Alternatives HFSQL</h1>

        <div class="critical">
            <h2>🚨 Situation actuelle</h2>
            <p><strong>Problème persistant :</strong> L'accès ODBC à HFSQL ne fonctionne pas malgré la configuration.</p>
            <p><strong>Cause probable :</strong> Incompatibilité entre la version du driver ODBC et le serveur HFSQL, ou limitation de la version HFSQL utilisée.</p>
        </div>

        <?php
        // Test final de diagnostic
        echo "<div class='test-section'>";
        echo "<h2>🔍 Diagnostic final</h2>";
        
        // Vérifier la version exacte du driver HFSQL
        echo "<h3>Version du driver HFSQL</h3>";
        $driver_info = shell_exec('powershell -Command "Get-OdbcDriver | Where-Object {$_.Name -eq \'HFSQL\'} | Select-Object Name, Version, Platform | Format-List" 2>nul');
        if ($driver_info) {
            echo "<div class='info'>Informations du driver :</div>";
            echo "<pre>" . htmlspecialchars($driver_info) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Impossible d'obtenir les détails du driver</div>";
        }
        
        // Test de la DLL du driver
        echo "<h3>Fichiers du driver HFSQL</h3>";
        $driver_files = shell_exec('dir "C:\Windows\System32\*hf*.dll" /b 2>nul');
        if (!$driver_files) {
            $driver_files = shell_exec('dir "C:\Windows\SysWOW64\*hf*.dll" /b 2>nul');
        }
        if ($driver_files) {
            echo "<div class='info'>DLL HFSQL trouvées :</div>";
            echo "<pre>" . htmlspecialchars($driver_files) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Aucune DLL HFSQL trouvée dans System32</div>";
        }
        
        echo "</div>";
        ?>

        <div class="test-section">
            <h2>🎯 Solutions alternatives</h2>

            <div class="solution">
                <h3>💡 Solution 1 : Utiliser l'accès natif HFSQL (Recommandé)</h3>
                <p>Au lieu d'ODBC, utilisez l'accès natif HFSQL via les fonctions WinDev/WebDev :</p>
                
                <div class="step">
                    <h4>Option A : Extension PHP HFSQL native</h4>
                    <p>Si vous avez accès aux outils PC SOFT :</p>
                    <ol>
                        <li>Utilisez <strong>WebDev</strong> pour créer une API REST</li>
                        <li>L'API WebDev accède nativement à HFSQL</li>
                        <li>Votre PHP appelle cette API via HTTP/JSON</li>
                    </ol>
                    
                    <div class="code">
                        // Exemple d'appel API REST<br>
                        $url = "http://localhost:8080/api/imprimantes";<br>
                        $response = file_get_contents($url);<br>
                        $data = json_decode($response, true);
                    </div>
                </div>

                <div class="step">
                    <h4>Option B : Fichiers d'échange</h4>
                    <p>Créez un pont via des fichiers :</p>
                    <ol>
                        <li>Application WinDev exporte les données vers JSON/XML</li>
                        <li>PHP lit ces fichiers d'échange</li>
                        <li>Synchronisation via tâches planifiées</li>
                    </ol>
                </div>
            </div>

            <div class="solution">
                <h3>💡 Solution 2 : Réinstaller le driver ODBC HFSQL</h3>
                
                <div class="step">
                    <h4>Étapes de réinstallation :</h4>
                    <ol>
                        <li><strong>Désinstallez</strong> le driver ODBC HFSQL actuel</li>
                        <li><strong>Téléchargez</strong> la dernière version depuis PC SOFT</li>
                        <li><strong>Installez</strong> la version correspondant à votre serveur HFSQL</li>
                        <li><strong>Vérifiez</strong> l'architecture (64-bit pour PHP 64-bit)</li>
                        <li><strong>Redémarrez</strong> le serveur web</li>
                    </ol>
                </div>

                <div class="step">
                    <h4>Commandes de désinstallation :</h4>
                    <div class="code">
                        # Supprimer le driver ODBC<br>
                        powershell -Command "Remove-OdbcDriver -Name 'HFSQL' -Platform '64-bit'"<br>
                        <br>
                        # Nettoyer le registre<br>
                        reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\ODBC\ODBCINST.INI\HFSQL" /f
                    </div>
                </div>
            </div>

            <div class="solution">
                <h3>💡 Solution 3 : Utiliser un autre driver de base de données</h3>
                
                <div class="step">
                    <h4>Migration vers MySQL/PostgreSQL :</h4>
                    <ol>
                        <li><strong>Exportez</strong> les données HFSQL vers SQL</li>
                        <li><strong>Importez</strong> dans MySQL ou PostgreSQL</li>
                        <li><strong>Utilisez</strong> les drivers ODBC MySQL/PostgreSQL (plus stables)</li>
                        <li><strong>Synchronisez</strong> périodiquement avec HFSQL</li>
                    </ol>
                </div>

                <div class="step">
                    <h4>Code PHP avec MySQL :</h4>
                    <div class="code">
                        &lt;?php<br>
                        // Connexion MySQL (plus fiable)<br>
                        $pdo = new PDO("mysql:host=localhost;dbname=datacafe", "user", "pass");<br>
                        $stmt = $pdo->query("SELECT * FROM imprimantes");<br>
                        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);<br>
                        ?&gt;
                    </div>
                </div>
            </div>

            <div class="solution">
                <h3>💡 Solution 4 : Créer un service de synchronisation</h3>
                
                <div class="step">
                    <h4>Architecture recommandée :</h4>
                    <ol>
                        <li><strong>Service Windows</strong> en WinDev qui lit HFSQL</li>
                        <li><strong>Synchronise</strong> vers une base MySQL/SQLite</li>
                        <li><strong>PHP</strong> accède à la base synchronisée</li>
                        <li><strong>Temps réel</strong> ou synchronisation périodique</li>
                    </ol>
                </div>

                <div class="step">
                    <h4>Avantages :</h4>
                    <ul>
                        <li>✅ Accès natif HFSQL côté WinDev</li>
                        <li>✅ Accès standard SQL côté PHP</li>
                        <li>✅ Performance optimale</li>
                        <li>✅ Fiabilité maximale</li>
                    </ul>
                </div>
            </div>

            <div class="solution">
                <h3>💡 Solution 5 : Contacter le support PC SOFT</h3>
                
                <div class="step">
                    <h4>Informations à fournir au support :</h4>
                    <ul>
                        <li><strong>Version HFSQL :</strong> (vérifiez dans le Centre de Contrôle)</li>
                        <li><strong>Version driver ODBC :</strong> (voir diagnostic ci-dessus)</li>
                        <li><strong>Erreur exacte :</strong> [S1000] "Chaîne de connexion insuffisante"</li>
                        <li><strong>OS :</strong> <?php echo PHP_OS; ?></li>
                        <li><strong>PHP :</strong> <?php echo PHP_VERSION . " (" . (PHP_INT_SIZE * 8) . " bits)"; ?></li>
                        <li><strong>Situation :</strong> Serveur HFSQL fonctionne, tables visibles, mais ODBC échoue</li>
                    </ul>
                </div>

                <div class="step">
                    <h4>Questions spécifiques à poser :</h4>
                    <ol>
                        <li>Ma version de HFSQL supporte-t-elle l'accès ODBC ?</li>
                        <li>Quelle version du driver ODBC dois-je utiliser ?</li>
                        <li>Y a-t-il des paramètres spéciaux à configurer ?</li>
                        <li>Existe-t-il des alternatives à ODBC pour PHP ?</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Recommandation finale</h2>
            
            <div class="info">
                <h3>💡 Ma recommandation personnelle</h3>
                <p>Basé sur mon expérience, je recommande la <strong>Solution 1 (API REST)</strong> :</p>
                <ol>
                    <li><strong>Créez une API WebDev</strong> qui accède nativement à HFSQL</li>
                    <li><strong>Votre PHP</strong> appelle cette API via HTTP</li>
                    <li><strong>Avantages :</strong>
                        <ul>
                            <li>✅ Accès natif HFSQL (100% compatible)</li>
                            <li>✅ Pas de problème de driver</li>
                            <li>✅ Architecture moderne et scalable</li>
                            <li>✅ Facilite la maintenance</li>
                        </ul>
                    </li>
                </ol>
            </div>

            <div class="warning">
                <h3>⚠️ Pourquoi ODBC pose problème</h3>
                <p>L'accès ODBC à HFSQL est souvent problématique car :</p>
                <ul>
                    <li>HFSQL est optimisé pour l'accès natif WinDev</li>
                    <li>Le driver ODBC peut être limité selon la version</li>
                    <li>Les incompatibilités d'architecture sont fréquentes</li>
                    <li>La configuration est complexe et fragile</li>
                </ul>
            </div>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>📞 Prochaines étapes</h2>
            <p>
                <strong>Option 1 :</strong> <a href="mailto:<EMAIL>" style="color: #007bff;">Contactez le support PC SOFT</a> avec les informations ci-dessus<br>
                <strong>Option 2 :</strong> Implémentez une solution alternative (API REST recommandée)<br>
                <strong>Option 3 :</strong> Migrez vers une base de données avec un meilleur support ODBC
            </p>
        </div>
    </div>
</body>
</html>
