<?php
/**
 * Équivalent PHP simple du code WinDev :
 * POUR TOUT Categorie
 *     ZoneRépétéeAjouteLigne(ZR_cat,Categorie.categories,Categorie.photo,Categorie.IDCategorie)
 * FIN
 */

// Fonction pour récupérer les catégories (remplacez par votre source de données)
function getCategoriesFromHFSQL() {
    // Option 1 : Depuis votre API WebDev (quand elle sera prête)
    /*
    try {
        $response = file_get_contents('http://localhost:8080/api/categories.awp');
        $data = json_decode($response, true);
        if (!$data['error']) {
            return $data['data'];
        }
    } catch (Exception $e) {
        // Fallback vers données de test
    }
    */
    
    // Option 2 : Données de simulation (remplacez par vos vraies données)
    return [
        ['IDCategorie' => 1, 'categories' => 'Boissons chaudes', 'photo' => 'images/boissons_chaudes.jpg'],
        ['IDCategorie' => 2, 'categories' => 'Boissons froides', 'photo' => 'images/boissons_froides.jpg'],
        ['IDCategorie' => 3, 'categories' => 'Pâtisseries', 'photo' => 'images/patisseries.jpg'],
        ['IDCategorie' => 4, 'categories' => 'Sandwichs', 'photo' => 'images/sandwichs.jpg'],
        ['IDCategorie' => 5, 'categories' => 'Salades', 'photo' => 'images/salades.jpg'],
        ['IDCategorie' => 6, 'categories' => 'Desserts', 'photo' => ''],
    ];
}

// Fonction équivalente à ZoneRépétéeAjouteLigne
function ajouterLigneCategorie($nom, $photo, $id) {
    echo "<div class='categorie-item' data-id='$id'>";
    
    // Photo
    if (!empty($photo) && file_exists($photo)) {
        echo "<img src='$photo' alt='$nom' class='categorie-photo'>";
    } else {
        echo "<div class='categorie-no-photo'>📷</div>";
    }
    
    // Nom de la catégorie
    echo "<h3 class='categorie-nom'>" . htmlspecialchars($nom) . "</h3>";
    
    // ID
    echo "<span class='categorie-id'>ID: $id</span>";
    
    echo "</div>";
}

// === EXÉCUTION ÉQUIVALENTE ===

// CSS pour l'affichage
echo "<style>
.categories-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}
.categorie-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.categorie-photo {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}
.categorie-no-photo {
    width: 100%;
    height: 120px;
    background: #f8f9fa;
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    border-radius: 5px;
    margin-bottom: 10px;
}
.categorie-nom {
    margin: 10px 0 5px 0;
    color: #333;
    font-size: 16px;
}
.categorie-id {
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
}
</style>";

echo "<h2>Catégories</h2>";
echo "<div class='categories-container'>";

// POUR TOUT Categorie (équivalent exact)
$categories = getCategoriesFromHFSQL();
$compteur = 0;

foreach ($categories as $categorie) {
    // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
    ajouterLigneCategorie(
        $categorie['categories'],    // Categorie.categories
        $categorie['photo'],         // Categorie.photo
        $categorie['IDCategorie']    // Categorie.IDCategorie
    );
    $compteur++;
}

echo "</div>";

// Équivalent de ZoneRépétéeOccurrence
echo "<p><strong>Nombre de catégories :</strong> $compteur</p>";

// === FONCTIONS UTILITAIRES SUPPLÉMENTAIRES ===

// Fonction pour récupérer une catégorie spécifique (équivalent de HLitPremier)
function getCategorieById($id) {
    $categories = getCategoriesFromHFSQL();
    foreach ($categories as $categorie) {
        if ($categorie['IDCategorie'] == $id) {
            return $categorie;
        }
    }
    return null;
}

// Fonction pour filtrer les catégories (équivalent de HFiltre)
function getCategoriesFiltered($critere) {
    $categories = getCategoriesFromHFSQL();
    return array_filter($categories, function($cat) use ($critere) {
        return stripos($cat['categories'], $critere) !== false;
    });
}

// Exemples d'utilisation des fonctions utilitaires
echo "<hr>";
echo "<h3>Exemples d'utilisation :</h3>";

// Récupérer une catégorie spécifique
$categorie = getCategorieById(1);
if ($categorie) {
    echo "<p>Catégorie ID 1 : " . $categorie['categories'] . "</p>";
}

// Filtrer les catégories
$categoriesBoissons = getCategoriesFiltered('Boissons');
echo "<p>Catégories contenant 'Boissons' : " . count($categoriesBoissons) . "</p>";

?>

<!-- 
UTILISATION DANS VOS PAGES :

1. Incluez ce fichier :
   <?php include 'categories_simple.php'; ?>

2. Ou utilisez les fonctions individuellement :
   <?php
   $categories = getCategoriesFromHFSQL();
   foreach ($categories as $cat) {
       echo $cat['categories'] . "<br>";
   }
   ?>

3. Pour une catégorie spécifique :
   <?php
   $categorie = getCategorieById($_GET['id']);
   if ($categorie) {
       echo $categorie['categories'];
   }
   ?>
-->
