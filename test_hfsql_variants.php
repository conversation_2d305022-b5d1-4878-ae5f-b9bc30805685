<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Variantes DSN HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .dsn-code { font-family: monospace; font-size: 12px; max-width: 400px; word-break: break-all; }
        .result-success { background-color: #d4edda; color: #155724; }
        .result-error { background-color: #f8d7da; color: #721c24; }
        .result-warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Variantes DSN HFSQL</h1>

        <?php
        // Configuration HFSQL
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => '',
            'driver' => 'HFSQL'
        ];

        echo "<div class='info'>";
        echo "<h2>📋 Configuration de test</h2>";
        echo "<ul>";
        echo "<li><strong>Serveur:</strong> {$config['server']}</li>";
        echo "<li><strong>Port:</strong> {$config['port']}</li>";
        echo "<li><strong>Base de données:</strong> {$config['database']}</li>";
        echo "<li><strong>Utilisateur:</strong> {$config['username']}</li>";
        echo "<li><strong>Driver:</strong> {$config['driver']}</li>";
        echo "</ul>";
        echo "</div>";

        // Différentes variantes de DSN à tester
        $dsn_variants = [
            // Variantes de base
            [
                'name' => 'Standard complet',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Sans base de données',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Sans utilisateur/mot de passe',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']}"
            ],
            [
                'name' => 'Minimal (serveur + port)',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']}"
            ],
            
            // Variantes de paramètres
            [
                'name' => 'Data Source au lieu de Server',
                'dsn' => "Driver={{$config['driver']}};Data Source={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'HOST/PORT/DB/USER/PASS',
                'dsn' => "Driver={{$config['driver']}};HOST={$config['server']};PORT={$config['port']};DB={$config['database']};USER={$config['username']};PASS={$config['password']}"
            ],
            [
                'name' => 'Initial Catalog + User ID',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Initial Catalog={$config['database']};User ID={$config['username']};Password={$config['password']}"
            ],
            
            // Variantes avec options supplémentaires
            [
                'name' => 'Avec Timeout',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']};Timeout=30"
            ],
            [
                'name' => 'Avec Trusted Connection',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};Trusted_Connection=yes"
            ],
            [
                'name' => 'Avec MARS (Multiple Active Result Sets)',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']};MARS_Connection=yes"
            ],
            
            // Variantes d'adresse
            [
                'name' => 'Localhost au lieu de 127.0.0.1',
                'dsn' => "Driver={{$config['driver']}};Server=localhost;Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Sans port (défaut)',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Variantes de format
            [
                'name' => 'Format URL-like',
                'dsn' => "Driver={{$config['driver']}};Server={$config['server']}:{$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Avec espaces autour des =',
                'dsn' => "Driver = {{$config['driver']}}; Server = {$config['server']}; Port = {$config['port']}; Database = {$config['database']}; UID = {$config['username']}; PWD = {$config['password']}"
            ]
        ];

        echo "<div class='info'>";
        echo "<h2>🧪 Test des variantes DSN</h2>";
        echo "<table>";
        echo "<tr><th style='width: 200px;'>Variante</th><th>DSN</th><th style='width: 100px;'>Résultat</th><th>Temps (ms)</th><th>Détails</th></tr>";

        $successful_dsn = null;
        $connection_details = [];

        foreach ($dsn_variants as $variant) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($variant['name']) . "</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($variant['dsn']) . "</td>";

            // Test de connexion
            $start_time = microtime(true);
            $connection = @odbc_connect($variant['dsn'], $config['username'], $config['password']);
            $connection_time = round((microtime(true) - $start_time) * 1000, 2);

            if ($connection) {
                echo "<td class='result-success'>✅ Succès</td>";
                echo "<td>{$connection_time}</td>";
                echo "<td class='result-success'>Connexion établie</td>";
                
                if (!$successful_dsn) {
                    $successful_dsn = $variant;
                }
                
                // Test d'une requête simple
                $query_result = @odbc_exec($connection, "SELECT 1 AS test");
                if ($query_result) {
                    $connection_details[] = "✅ Requête SELECT 1 réussie";
                } else {
                    $connection_details[] = "⚠️ Connexion OK mais requête échouée";
                }
                
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                echo "<td class='result-error'>❌ Échec</td>";
                echo "<td>{$connection_time}</td>";
                
                if (strpos($error, 'introuvable') !== false || strpos($error, 'not found') !== false) {
                    echo "<td class='result-error'>Driver non trouvé</td>";
                } elseif (strpos($error, 'insuffisant') !== false) {
                    echo "<td class='result-warning'>Chaîne insuffisante</td>";
                } elseif (strpos($error, 'connexion') !== false) {
                    echo "<td class='result-warning'>Erreur de connexion</td>";
                } else {
                    echo "<td class='result-error'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
                }
            }
            echo "</tr>";
        }

        echo "</table>";
        echo "</div>";

        // Résumé des résultats
        if ($successful_dsn) {
            echo "<div class='success'>";
            echo "<h2>🎉 Connexion réussie !</h2>";
            echo "<p><strong>DSN fonctionnel:</strong> " . htmlspecialchars($successful_dsn['name']) . "</p>";
            echo "<div class='dsn-code' style='background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
            echo htmlspecialchars($successful_dsn['dsn']);
            echo "</div>";
            
            if (!empty($connection_details)) {
                echo "<p><strong>Tests supplémentaires:</strong></p>";
                echo "<ul>";
                foreach ($connection_details as $detail) {
                    echo "<li>$detail</li>";
                }
                echo "</ul>";
            }
            
            echo "<h3>💻 Code à utiliser dans votre application:</h3>";
            echo "<div class='dsn-code' style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>";
            echo htmlspecialchars('$dsn = "' . $successful_dsn['dsn'] . '";') . "<br>";
            echo htmlspecialchars('$connection = odbc_connect($dsn, "' . $config['username'] . '", "' . $config['password'] . '");');
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Aucune connexion réussie</h2>";
            echo "<p>Aucune des variantes de DSN testées n'a fonctionné.</p>";
            echo "<h3>🔧 Suggestions:</h3>";
            echo "<ul>";
            echo "<li>Vérifiez que le serveur HFSQL WinDev est démarré</li>";
            echo "<li>Vérifiez que le port 4900 est ouvert et accessible</li>";
            echo "<li>Vérifiez que la base de données 'DataCafe' existe</li>";
            echo "<li>Vérifiez les identifiants de connexion</li>";
            echo "<li>Essayez de créer un DSN système dans Windows</li>";
            echo "<li>Consultez la documentation HFSQL pour la syntaxe exacte</li>";
            echo "</ul>";
            echo "</div>";
        }

        // Informations système
        echo "<div class='info'>";
        echo "<h2>ℹ️ Informations système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP:</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture:</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>Extension ODBC:</strong> " . (extension_loaded('odbc') ? 'Chargée' : 'Non chargée') . "</li>";
        echo "<li><strong>Date/Heure:</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔄 Relancer le test</a></p>
        </div>
    </div>
</body>
</html>
