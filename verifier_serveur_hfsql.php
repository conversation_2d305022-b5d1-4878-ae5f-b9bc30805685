<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Serveur HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .command { background-color: #e9ecef; padding: 8px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vérification du Serveur HFSQL</h1>

        <?php
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe'
        ];

        // Test 1: Recherche des processus HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔍 1. Processus HFSQL en cours</h2>";
        
        $process_searches = [
            'manta' => 'tasklist /FI "IMAGENAME eq manta*" /FO CSV',
            'hf' => 'tasklist /FI "IMAGENAME eq hf*" /FO CSV',
            'windev' => 'tasklist /FI "IMAGENAME eq windev*" /FO CSV',
            'pcsoft' => 'tasklist /FI "IMAGENAME eq *pcsoft*" /FO CSV',
            'tous' => 'tasklist /FO CSV'
        ];
        
        $found_processes = [];
        
        foreach ($process_searches as $search_name => $command) {
            if ($search_name === 'tous') continue; // Skip pour l'instant
            
            echo "<h3>Recherche: $search_name</h3>";
            echo "<div class='command'>$command</div>";
            
            $result = shell_exec($command . ' 2>nul');
            if ($result && strlen(trim($result)) > 50) { // Plus que juste l'en-tête
                echo "<div class='success'>✅ Processus trouvés:</div>";
                echo "<pre>" . htmlspecialchars($result) . "</pre>";
                $found_processes[] = $search_name;
            } else {
                echo "<div class='warning'>⚠️ Aucun processus trouvé pour '$search_name'</div>";
            }
        }
        
        if (empty($found_processes)) {
            echo "<div class='error'>❌ Aucun processus HFSQL détecté</div>";
            echo "<div class='info'>Le serveur HFSQL n'est probablement pas démarré</div>";
        }
        echo "</div>";

        // Test 2: Test de connectivité réseau détaillé
        echo "<div class='test-section'>";
        echo "<h2>🌐 2. Connectivité réseau</h2>";
        
        // Test ping
        echo "<h3>Test ping</h3>";
        $ping_command = "ping -n 2 {$config['server']}";
        echo "<div class='command'>$ping_command</div>";
        $ping_result = shell_exec($ping_command . ' 2>&1');
        
        if (strpos($ping_result, 'TTL=') !== false) {
            echo "<div class='success'>✅ Ping réussi vers {$config['server']}</div>";
        } else {
            echo "<div class='error'>❌ Ping échoué</div>";
            echo "<pre>" . htmlspecialchars($ping_result) . "</pre>";
        }
        
        // Test du port spécifique
        echo "<h3>Test du port {$config['port']}</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($socket) {
            fclose($socket);
            echo "<div class='success'>✅ Port {$config['port']} accessible</div>";
        } else {
            echo "<div class='error'>❌ Port {$config['port']} inaccessible</div>";
            echo "<div class='warning'>Erreur: $errstr ($errno)</div>";
        }
        
        // Vérification avec netstat
        echo "<h3>Ports en écoute</h3>";
        $netstat_command = "netstat -an | findstr :4900";
        echo "<div class='command'>$netstat_command</div>";
        $netstat_result = shell_exec($netstat_command);
        
        if ($netstat_result) {
            echo "<div class='success'>✅ Port 4900 trouvé dans netstat:</div>";
            echo "<pre>" . htmlspecialchars($netstat_result) . "</pre>";
        } else {
            echo "<div class='error'>❌ Port 4900 non trouvé dans netstat</div>";
            
            // Chercher d'autres ports courants
            echo "<h4>Recherche d'autres ports HFSQL courants:</h4>";
            $common_ports = [4900, 4901, 4902, 4999, 5000, 8080, 8000, 1433, 3306];
            
            foreach ($common_ports as $port) {
                $port_check = shell_exec("netstat -an | findstr :$port");
                if ($port_check) {
                    echo "<div class='info'>✅ Port $port en écoute: " . trim($port_check) . "</div>";
                }
            }
        }
        echo "</div>";

        // Test 3: Tentative de connexion telnet
        echo "<div class='test-section'>";
        echo "<h2>🔌 3. Test de connexion directe</h2>";
        
        echo "<h3>Test socket PHP</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 10);
        if ($socket) {
            echo "<div class='success'>✅ Connexion socket réussie</div>";
            
            // Essayer de lire une réponse
            stream_set_timeout($socket, 2);
            $response = @fread($socket, 1024);
            if ($response) {
                echo "<div class='info'>Réponse du serveur: " . htmlspecialchars(substr($response, 0, 100)) . "</div>";
            } else {
                echo "<div class='info'>Connexion établie mais aucune réponse immédiate</div>";
            }
            fclose($socket);
        } else {
            echo "<div class='error'>❌ Connexion socket échouée: $errstr ($errno)</div>";
        }
        
        // Test avec différents ports
        echo "<h3>Test de ports alternatifs</h3>";
        $test_ports = [4900, 4901, 4902, 4999, 5000];
        foreach ($test_ports as $port) {
            $test_socket = @fsockopen($config['server'], $port, $errno, $errstr, 2);
            if ($test_socket) {
                fclose($test_socket);
                echo "<div class='success'>✅ Port $port accessible</div>";
            } else {
                echo "<div class='warning'>⚠️ Port $port inaccessible</div>";
            }
        }
        echo "</div>";

        // Test 4: Recherche d'installations HFSQL
        echo "<div class='test-section'>";
        echo "<h2>📦 4. Recherche d'installations HFSQL/WinDev</h2>";
        
        $search_paths = [
            'C:\Program Files\PC SOFT',
            'C:\Program Files (x86)\PC SOFT',
            'C:\WinDev',
            'C:\HFSQL',
            'C:\Program Files\WinDev',
            'C:\Program Files (x86)\WinDev',
            'C:\Program Files\PCSOFT',
            'C:\Program Files (x86)\PCSOFT'
        ];
        
        $found_installations = [];
        
        foreach ($search_paths as $path) {
            if (is_dir($path)) {
                echo "<div class='success'>✅ Trouvé: $path</div>";
                $found_installations[] = $path;
                
                // Lister les sous-dossiers
                $subdirs = glob($path . '\*', GLOB_ONLYDIR);
                if ($subdirs) {
                    $subdir_names = array_map('basename', array_slice($subdirs, 0, 5));
                    echo "<div class='info'>Sous-dossiers: " . implode(', ', $subdir_names) . "</div>";
                }
                
                // Chercher des exécutables HFSQL
                $executables = glob($path . '\**\*.exe');
                $hfsql_exes = array_filter($executables, function($exe) {
                    $basename = strtolower(basename($exe));
                    return strpos($basename, 'hf') !== false || 
                           strpos($basename, 'manta') !== false || 
                           strpos($basename, 'server') !== false;
                });
                
                if ($hfsql_exes) {
                    echo "<div class='info'>Exécutables HFSQL trouvés: " . implode(', ', array_map('basename', array_slice($hfsql_exes, 0, 3))) . "</div>";
                }
            }
        }
        
        if (empty($found_installations)) {
            echo "<div class='warning'>⚠️ Aucune installation HFSQL/WinDev trouvée dans les emplacements standards</div>";
        }
        echo "</div>";

        // Test 5: Recommandations
        echo "<div class='test-section'>";
        echo "<h2>💡 5. Recommandations</h2>";
        
        if (empty($found_processes)) {
            echo "<div class='error'>";
            echo "<h3>🚨 Serveur HFSQL non démarré</h3>";
            echo "<p>Le serveur HFSQL ne semble pas être en cours d'exécution.</p>";
            echo "<h4>Actions à effectuer :</h4>";
            echo "<ol>";
            echo "<li><strong>Démarrer le serveur HFSQL :</strong>";
            echo "<ul>";
            echo "<li>Ouvrez le Centre de Contrôle HFSQL</li>";
            echo "<li>Ou lancez manuellement le serveur HFSQL</li>";
            echo "<li>Ou utilisez les services Windows si HFSQL est installé comme service</li>";
            echo "</ul></li>";
            echo "<li><strong>Vérifier la configuration :</strong>";
            echo "<ul>";
            echo "<li>Port d'écoute (par défaut 4900)</li>";
            echo "<li>Adresse d'écoute (0.0.0.0 ou 127.0.0.1)</li>";
            echo "<li>Base de données '{$config['database']}' existe</li>";
            echo "</ul></li>";
            echo "</ol>";
            echo "</div>";
        }
        
        if (!empty($found_installations)) {
            echo "<div class='info'>";
            echo "<h3>📂 Installations trouvées</h3>";
            echo "<p>Des installations HFSQL/WinDev ont été trouvées. Vérifiez dans ces dossiers :</p>";
            echo "<ul>";
            foreach ($found_installations as $installation) {
                echo "<li><code>$installation</code></li>";
            }
            echo "</ul>";
            echo "<p>Cherchez des fichiers comme :</p>";
            echo "<ul>";
            echo "<li><code>HFSQLServer.exe</code> - Serveur HFSQL</li>";
            echo "<li><code>MantaManager.exe</code> - Centre de contrôle</li>";
            echo "<li><code>HFSQL Control Center.exe</code> - Centre de contrôle</li>";
            echo "</ul>";
            echo "</div>";
        }
        
        echo "<div class='warning'>";
        echo "<h3>🔧 Étapes de dépannage</h3>";
        echo "<ol>";
        echo "<li><strong>Démarrer le serveur HFSQL</strong> si ce n'est pas fait</li>";
        echo "<li><strong>Vérifier la configuration du serveur</strong> (port, base de données)</li>";
        echo "<li><strong>Tester avec l'outil d'administration HFSQL</strong> avant PHP</li>";
        echo "<li><strong>Vérifier le pare-feu Windows</strong> pour le port 4900</li>";
        echo "<li><strong>Consulter les logs du serveur HFSQL</strong> pour les erreurs</li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer la vérification</a>
                <a href="test_chaines_hfsql.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🧪 Tester les chaînes de connexion</a>
            </p>
        </div>
    </div>
</body>
</html>
