<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Drivers ODBC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .code { background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; margin: 10px 0; border-left: 4px solid #007bff; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .driver-found { background-color: #d4edda; }
        .driver-missing { background-color: #f8d7da; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 4px; border-left: 4px solid #28a745; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic des Drivers ODBC</h1>
        
        <?php
        // Test de l'extension ODBC
        if (!extension_loaded('odbc')) {
            echo "<div class='error'>";
            echo "<h2>❌ Extension ODBC non disponible</h2>";
            echo "<p><a href='install_odbc.php'>Retour à l'installation ODBC</a></p>";
            echo "</div></div></body></html>";
            exit;
        }
        
        echo "<div class='success'>";
        echo "<h2>✅ Extension ODBC disponible</h2>";
        echo "<p>Version ODBC: " . phpversion('odbc') . "</p>";
        echo "</div>";
        
        // Fonction pour lister les drivers ODBC
        function listerDriversODBC() {
            $drivers = [];
            
            // Méthode 1: Via odbc_drivers (si disponible)
            if (function_exists('odbc_drivers')) {
                $odbc_drivers = odbc_drivers();
                if ($odbc_drivers && is_array($odbc_drivers)) {
                    foreach ($odbc_drivers as $driver) {
                        if (isset($driver['DRIVER'])) {
                            $drivers[] = [
                                'name' => $driver['DRIVER'],
                                'source' => 'odbc_drivers()',
                                'details' => $driver
                            ];
                        }
                    }
                }
            }
            
            // Méthode 2: Via registre Windows (si accessible)
            if (PHP_OS_FAMILY === 'Windows') {
                try {
                    // Tentative de lecture du registre via WMI ou autres méthodes
                    $registry_drivers = lireRegistreDrivers();
                    $drivers = array_merge($drivers, $registry_drivers);
                } catch (Exception $e) {
                    // Ignorer les erreurs de registre
                }
            }
            
            return $drivers;
        }
        
        // Fonction pour lire les drivers depuis le registre (Windows)
        function lireRegistreDrivers() {
            $drivers = [];
            
            // Liste des drivers ODBC courants à tester
            $common_drivers = [
                'HFSQL ODBC Driver',
                'HFSQL',
                'Microsoft Access Driver (*.mdb, *.accdb)',
                'Microsoft Excel Driver (*.xls, *.xlsx, *.xlsm, *.xlsb)',
                'SQL Server',
                'SQL Server Native Client 11.0',
                'MySQL ODBC 8.0 Driver',
                'PostgreSQL ANSI',
                'PostgreSQL Unicode',
                'Oracle in OraClient12Home1',
                'Microsoft ODBC for Oracle'
            ];
            
            foreach ($common_drivers as $driver_name) {
                // Test de connexion pour vérifier si le driver existe
                $test_dsn = "Driver={$driver_name};";
                $test_result = @odbc_connect($test_dsn, '', '');
                
                if ($test_result !== false) {
                    odbc_close($test_result);
                    $drivers[] = [
                        'name' => $driver_name,
                        'source' => 'Test de connexion',
                        'status' => 'Disponible'
                    ];
                } else {
                    $error = odbc_errormsg();
                    // Si l'erreur n'est pas "driver not found", le driver existe probablement
                    if (strpos($error, 'introuvable') === false && strpos($error, 'not found') === false) {
                        $drivers[] = [
                            'name' => $driver_name,
                            'source' => 'Test de connexion',
                            'status' => 'Trouvé (erreur de connexion: ' . substr($error, 0, 50) . '...)'
                        ];
                    }
                }
            }
            
            return $drivers;
        }
        
        // Lister les drivers disponibles
        echo "<div class='info'>";
        echo "<h2>🔌 Drivers ODBC disponibles</h2>";
        
        $drivers = listerDriversODBC();
        
        if (!empty($drivers)) {
            echo "<table>";
            echo "<tr><th>Driver</th><th>Source</th><th>Statut</th></tr>";
            
            $hfsql_found = false;
            foreach ($drivers as $driver) {
                $is_hfsql = (stripos($driver['name'], 'hfsql') !== false);
                if ($is_hfsql) $hfsql_found = true;
                
                $row_class = $is_hfsql ? 'driver-found' : '';
                echo "<tr class='$row_class'>";
                echo "<td><strong>" . htmlspecialchars($driver['name']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($driver['source']) . "</td>";
                echo "<td>" . htmlspecialchars($driver['status'] ?? 'Disponible') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($hfsql_found) {
                echo "<div class='success'>✅ Driver HFSQL trouvé !</div>";
            } else {
                echo "<div class='error'>❌ Aucun driver HFSQL trouvé</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ Impossible de lister les drivers ODBC automatiquement</div>";
        }
        echo "</div>";
        
        // Test de différentes variantes de DSN
        echo "<div class='info'>";
        echo "<h2>🧪 Test des variantes de drivers HFSQL</h2>";
        
        $hfsql_variants = [
            'HFSQL ODBC Driver',
            'HFSQL',
            'HFSQL ODBC',
            'PC SOFT HFSQL',
            'WinDev HFSQL',
            'HFSQL Client/Server'
        ];
        
        echo "<table>";
        echo "<tr><th>Variante du driver</th><th>Test de connexion</th><th>Erreur</th></tr>";
        
        $working_driver = null;
        foreach ($hfsql_variants as $driver_variant) {
            $test_dsn = "Driver={$driver_variant};Server=127.0.0.1;Port=4900;Database=DataCafe;";
            
            echo "<tr>";
            echo "<td><code>$driver_variant</code></td>";
            
            $connection = @odbc_connect($test_dsn, 'admin', '');
            if ($connection) {
                echo "<td class='success'>✅ Connexion réussie</td>";
                echo "<td>-</td>";
                $working_driver = $driver_variant;
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                if (strpos($error, 'introuvable') !== false || strpos($error, 'not found') !== false) {
                    echo "<td class='error'>❌ Driver non trouvé</td>";
                } else {
                    echo "<td class='warning'>⚠️ Driver trouvé, erreur de connexion</td>";
                }
                echo "<td><small>" . htmlspecialchars(substr($error, 0, 80)) . "...</small></td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        if ($working_driver) {
            echo "<div class='success'>";
            echo "<h3>🎉 Driver fonctionnel trouvé !</h3>";
            echo "<p><strong>Driver à utiliser :</strong> <code>$working_driver</code></p>";
            echo "</div>";
        }
        echo "</div>";
        
        // Instructions d'installation du driver HFSQL
        if (!$working_driver) {
            echo "<div class='error'>";
            echo "<h2>📥 Installation du Driver HFSQL ODBC</h2>";
            echo "<p>Le driver HFSQL ODBC n'est pas installé sur votre système.</p>";
            echo "</div>";
            
            echo "<div class='step'>";
            echo "<h3>Étape 1: Télécharger le driver HFSQL ODBC</h3>";
            echo "<ol>";
            echo "<li>Visitez le site de PC SOFT : <a href='https://www.pcsoft.fr' target='_blank'>www.pcsoft.fr</a></li>";
            echo "<li>Recherchez 'HFSQL ODBC Driver' dans la section téléchargements</li>";
            echo "<li>Téléchargez la version correspondant à votre architecture (" . (PHP_INT_SIZE * 8) . " bits)</li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<div class='step'>";
            echo "<h3>Étape 2: Installer le driver</h3>";
            echo "<ol>";
            echo "<li>Exécutez l'installateur en tant qu'<strong>Administrateur</strong></li>";
            echo "<li>Suivez les instructions d'installation</li>";
            echo "<li>Redémarrez votre ordinateur si demandé</li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<div class='step'>";
            echo "<h3>Étape 3: Vérifier l'installation</h3>";
            echo "<ol>";
            echo "<li>Ouvrez 'Sources de données ODBC' dans Windows :</li>";
            echo "<ul>";
            echo "<li>Windows 10/11 : Recherchez 'ODBC' dans le menu Démarrer</li>";
            echo "<li>Ou allez dans Panneau de configuration → Outils d'administration → Sources de données ODBC</li>";
            echo "</ul>";
            echo "<li>Dans l'onglet 'Pilotes', vérifiez que 'HFSQL ODBC Driver' apparaît</li>";
            echo "<li><a href='" . $_SERVER['PHP_SELF'] . "'>🔄 Rechargez cette page</a> pour re-tester</li>";
            echo "</ol>";
            echo "</div>";
        }
        
        // Configuration alternative
        echo "<div class='info'>";
        echo "<h2>🔧 Solutions alternatives</h2>";
        
        echo "<h3>Option 1: Utiliser un autre driver (si disponible)</h3>";
        echo "<p>Si vous avez accès à la base HFSQL via un autre protocole :</p>";
        echo "<ul>";
        echo "<li><strong>MySQL/MariaDB :</strong> Si HFSQL peut exporter vers MySQL</li>";
        echo "<li><strong>PostgreSQL :</strong> Si une passerelle existe</li>";
        echo "<li><strong>Fichiers CSV/Excel :</strong> Pour l'import/export de données</li>";
        echo "</ul>";
        
        echo "<h3>Option 2: Configuration DSN système</h3>";
        echo "<p>Créer une source de données système dans Windows :</p>";
        echo "<ol>";
        echo "<li>Ouvrez 'Sources de données ODBC (64 bits)' ou '(32 bits)' selon votre PHP</li>";
        echo "<li>Onglet 'DSN Système' → 'Ajouter'</li>";
        echo "<li>Sélectionnez le driver HFSQL (une fois installé)</li>";
        echo "<li>Configurez avec vos paramètres de connexion</li>";
        echo "<li>Utilisez le nom du DSN au lieu du driver dans votre code</li>";
        echo "</ol>";
        
        echo "</div>";
        
        // Code de connexion mis à jour
        if ($working_driver) {
            echo "<div class='success'>";
            echo "<h2>💻 Code de connexion mis à jour</h2>";
            echo "<p>Utilisez ce code dans votre application :</p>";
            echo "<div class='code'>";
            echo htmlspecialchars('$dsn = "Driver={' . $working_driver . '};Server=127.0.0.1;Port=4900;Database=DataCafe;UID=admin;PWD=";') . "<br>";
            echo htmlspecialchars('$connection = odbc_connect($dsn, "admin", "");');
            echo "</div>";
            echo "</div>";
        }
        
        // Actions suivantes
        echo "<div class='info'>";
        echo "<h2>🚀 Actions suivantes</h2>";
        echo "<ol>";
        if (!$working_driver) {
            echo "<li><strong>Installez le driver HFSQL ODBC</strong> selon les instructions ci-dessus</li>";
            echo "<li><a href='" . $_SERVER['PHP_SELF'] . "'>🔄 Rechargez cette page</a> pour vérifier l'installation</li>";
        }
        echo "<li><a href='index.php'>🔙 Retournez au test de connexion HFSQL</a></li>";
        echo "<li>Si le problème persiste, vérifiez que le serveur HFSQL est démarré</li>";
        echo "</ol>";
        echo "</div>";
        ?>
        
        <div class="info">
            <h2>📞 Support</h2>
            <p>Si vous continuez à rencontrer des problèmes :</p>
            <ul>
                <li>Consultez la documentation PC SOFT pour HFSQL ODBC</li>
                <li>Vérifiez les logs d'erreur Windows (Observateur d'événements)</li>
                <li>Contactez le support PC SOFT pour l'installation du driver</li>
            </ul>
        </div>
    </div>
</body>
</html>
