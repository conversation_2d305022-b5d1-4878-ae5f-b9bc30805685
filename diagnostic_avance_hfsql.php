<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Avancé HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 11px; }
        .command { background-color: #e9ecef; padding: 8px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; font-size: 12px; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Diagnostic Avancé HFSQL</h1>

        <?php
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Test 1: Informations détaillées sur le driver HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔍 1. Analyse du driver HFSQL</h2>";
        
        // Vérification via le registre
        echo "<h3>Informations du registre ODBC</h3>";
        $reg_commands = [
            'Driver HFSQL' => 'reg query "HKEY_LOCAL_MACHINE\SOFTWARE\ODBC\ODBCINST.INI\HFSQL" /s',
            'Tous les drivers ODBC' => 'reg query "HKEY_LOCAL_MACHINE\SOFTWARE\ODBC\ODBCINST.INI" /f HFSQL /s'
        ];
        
        foreach ($reg_commands as $desc => $command) {
            echo "<h4>$desc</h4>";
            echo "<div class='command'>$command</div>";
            $result = shell_exec($command . ' 2>nul');
            if ($result) {
                echo "<div class='success'>✅ Informations trouvées:</div>";
                echo "<pre>" . htmlspecialchars($result) . "</pre>";
            } else {
                echo "<div class='warning'>⚠️ Aucune information trouvée</div>";
            }
        }
        
        // Test avec PowerShell pour plus de détails
        echo "<h3>Détails PowerShell du driver</h3>";
        $ps_command = 'Get-OdbcDriver | Where-Object {$_.Name -eq "HFSQL"} | Format-List *';
        echo "<div class='command'>powershell -Command \"$ps_command\"</div>";
        $ps_result = shell_exec('powershell -Command "' . $ps_command . '" 2>nul');
        if ($ps_result) {
            echo "<div class='success'>✅ Détails du driver HFSQL:</div>";
            echo "<pre>" . htmlspecialchars($ps_result) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Impossible d'obtenir les détails via PowerShell</div>";
        }
        echo "</div>";

        // Test 2: Test de connectivité réseau avancé
        echo "<div class='test-section'>";
        echo "<h2>🌐 2. Test de connectivité avancé</h2>";
        
        // Test avec différents timeouts
        echo "<h3>Test de connexion avec différents timeouts</h3>";
        $timeouts = [1, 5, 10, 30];
        foreach ($timeouts as $timeout) {
            $start_time = microtime(true);
            $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, $timeout);
            $elapsed = round((microtime(true) - $start_time) * 1000, 2);
            
            if ($socket) {
                fclose($socket);
                echo "<div class='success'>✅ Timeout {$timeout}s: Connexion réussie ({$elapsed}ms)</div>";
            } else {
                echo "<div class='error'>❌ Timeout {$timeout}s: Échec - $errstr ($errno) ({$elapsed}ms)</div>";
            }
        }
        
        // Test de lecture de données du serveur
        echo "<h3>Test de communication avec le serveur</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 10);
        if ($socket) {
            echo "<div class='success'>✅ Connexion socket établie</div>";
            
            // Essayer de lire la bannière du serveur
            stream_set_timeout($socket, 5);
            $banner = @fread($socket, 1024);
            if ($banner) {
                echo "<div class='info'>Bannière du serveur: " . htmlspecialchars(substr($banner, 0, 200)) . "</div>";
            } else {
                echo "<div class='warning'>⚠️ Aucune bannière reçue du serveur</div>";
            }
            
            // Essayer d'envoyer une commande simple
            @fwrite($socket, "HELLO\r\n");
            $response = @fread($socket, 1024);
            if ($response) {
                echo "<div class='info'>Réponse à HELLO: " . htmlspecialchars(substr($response, 0, 200)) . "</div>";
            }
            
            fclose($socket);
        } else {
            echo "<div class='error'>❌ Impossible d'établir une connexion socket</div>";
        }
        echo "</div>";

        // Test 3: Test ODBC avec debug détaillé
        echo "<div class='test-section'>";
        echo "<h2>🔧 3. Test ODBC avec debug détaillé</h2>";
        
        if (extension_loaded('odbc')) {
            // Test avec différentes configurations ODBC
            $debug_dsns = [
                'Minimal' => "Driver={HFSQL}",
                'Serveur seulement' => "Driver={HFSQL};Server={$config['server']}",
                'Serveur + Port' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']}",
                'Sans accolades' => "Driver=HFSQL;Server={$config['server']};Port={$config['port']}",
                'Format alternatif 1' => "Driver={HFSQL};HOST={$config['server']};PORT={$config['port']}",
                'Format alternatif 2' => "Driver={HFSQL};ServerName={$config['server']};ServerPort={$config['port']}",
                'Avec options' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Timeout=30;AutoCommit=1"
            ];
            
            echo "<table>";
            echo "<tr><th>Configuration</th><th>DSN</th><th>Résultat</th><th>Erreur détaillée</th></tr>";
            
            foreach ($debug_dsns as $name => $dsn) {
                echo "<tr>";
                echo "<td><strong>$name</strong></td>";
                echo "<td style='font-family: monospace; font-size: 10px;'>" . htmlspecialchars($dsn) . "</td>";
                
                // Test de connexion avec capture d'erreur détaillée
                $connection = @odbc_connect($dsn, $config['username'], $config['password']);
                if ($connection) {
                    echo "<td style='background-color: #d4edda;'>✅ Succès</td>";
                    echo "<td>-</td>";
                    odbc_close($connection);
                } else {
                    $error = odbc_errormsg();
                    $error_number = odbc_error();
                    echo "<td style='background-color: #f8d7da;'>❌ Échec</td>";
                    echo "<td style='font-size: 10px;'>[$error_number] " . htmlspecialchars($error) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ Extension ODBC non chargée</div>";
        }
        echo "</div>";

        // Test 4: Test avec PDO ODBC
        echo "<div class='test-section'>";
        echo "<h2>🔌 4. Test avec PDO ODBC</h2>";
        
        if (extension_loaded('pdo_odbc')) {
            $pdo_dsns = [
                'PDO Standard' => "odbc:Driver={HFSQL};Server={$config['server']};Port={$config['port']}",
                'PDO sans accolades' => "odbc:Driver=HFSQL;Server={$config['server']};Port={$config['port']}",
                'PDO avec base' => "odbc:Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}"
            ];
            
            echo "<table>";
            echo "<tr><th>Type</th><th>DSN PDO</th><th>Résultat</th><th>Erreur</th></tr>";
            
            foreach ($pdo_dsns as $name => $dsn) {
                echo "<tr>";
                echo "<td><strong>$name</strong></td>";
                echo "<td style='font-family: monospace; font-size: 10px;'>" . htmlspecialchars($dsn) . "</td>";
                
                try {
                    $pdo = new PDO($dsn, $config['username'], $config['password']);
                    echo "<td style='background-color: #d4edda;'>✅ Succès</td>";
                    echo "<td>-</td>";
                } catch (PDOException $e) {
                    echo "<td style='background-color: #f8d7da;'>❌ Échec</td>";
                    echo "<td style='font-size: 10px;'>" . htmlspecialchars($e->getMessage()) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ Extension PDO_ODBC non chargée</div>";
        }
        echo "</div>";

        // Test 5: Vérification de la configuration du serveur HFSQL
        echo "<div class='test-section'>";
        echo "<h2>⚙️ 5. Configuration du serveur HFSQL</h2>";
        
        echo "<h3>Processus HFSQL en cours</h3>";
        $processes = shell_exec('tasklist /FI "IMAGENAME eq *hf*" /FO CSV 2>nul');
        if (!$processes) {
            $processes = shell_exec('tasklist /FI "IMAGENAME eq *manta*" /FO CSV 2>nul');
        }
        if ($processes && strlen(trim($processes)) > 50) {
            echo "<div class='success'>✅ Processus HFSQL trouvés:</div>";
            echo "<pre>" . htmlspecialchars($processes) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Processus HFSQL non détectés clairement</div>";
        }
        
        echo "<h3>Ports en écoute</h3>";
        $netstat = shell_exec("netstat -an | findstr :4900");
        if ($netstat) {
            echo "<div class='success'>✅ Port 4900 en écoute:</div>";
            echo "<pre>" . htmlspecialchars($netstat) . "</pre>";
        } else {
            echo "<div class='error'>❌ Port 4900 non trouvé dans netstat</div>";
            
            // Chercher tous les ports en écoute
            echo "<h4>Tous les ports en écoute:</h4>";
            $all_ports = shell_exec("netstat -an | findstr LISTENING");
            if ($all_ports) {
                $lines = explode("\n", $all_ports);
                $relevant_ports = array_filter($lines, function($line) {
                    return strpos($line, ':49') !== false || 
                           strpos($line, ':50') !== false || 
                           strpos($line, ':80') !== false ||
                           strpos($line, ':443') !== false;
                });
                if ($relevant_ports) {
                    echo "<pre>" . htmlspecialchars(implode("\n", array_slice($relevant_ports, 0, 10))) . "</pre>";
                }
            }
        }
        echo "</div>";

        // Test 6: Recommandations spécifiques
        echo "<div class='test-section'>";
        echo "<h2>💡 6. Recommandations spécifiques</h2>";
        
        echo "<div class='warning'>";
        echo "<h3>🔧 Actions à effectuer</h3>";
        echo "<ol>";
        echo "<li><strong>Vérifiez la configuration du serveur HFSQL :</strong>";
        echo "<ul>";
        echo "<li>Ouvrez le Centre de Contrôle HFSQL</li>";
        echo "<li>Vérifiez que le serveur écoute sur 0.0.0.0:4900 (pas seulement localhost)</li>";
        echo "<li>Vérifiez que la base de données '{$config['database']}' existe</li>";
        echo "<li>Vérifiez que l'utilisateur '{$config['username']}' est configuré</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Testez avec l'outil d'administration HFSQL :</strong>";
        echo "<ul>";
        echo "<li>Connectez-vous avec les mêmes paramètres depuis l'outil PC SOFT</li>";
        echo "<li>Vérifiez que la connexion fonctionne</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Vérifiez la compatibilité du driver :</strong>";
        echo "<ul>";
        echo "<li>Architecture : PHP " . (PHP_INT_SIZE * 8) . " bits vs Driver ODBC</li>";
        echo "<li>Version du driver HFSQL compatible avec votre version de serveur</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Créez un DSN système :</strong>";
        echo "<ul>";
        echo "<li>Utilisez l'outil 'Sources de données ODBC' de Windows</li>";
        echo "<li>Créez un DSN système avec le driver HFSQL</li>";
        echo "<li>Testez la connexion dans l'interface ODBC</li>";
        echo "</ul></li>";
        
        echo "<li><strong>Vérifiez les logs :</strong>";
        echo "<ul>";
        echo "<li>Consultez les logs du serveur HFSQL</li>";
        echo "<li>Activez le mode debug si disponible</li>";
        echo "</ul></li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h3>📋 Informations à vérifier avec l'administrateur HFSQL</h3>";
        echo "<ul>";
        echo "<li><strong>Port d'écoute :</strong> Le serveur écoute-t-il vraiment sur le port 4900 ?</li>";
        echo "<li><strong>Adresse d'écoute :</strong> Le serveur accepte-t-il les connexions externes ?</li>";
        echo "<li><strong>Base de données :</strong> La base '{$config['database']}' existe-t-elle ?</li>";
        echo "<li><strong>Utilisateur :</strong> L'utilisateur '{$config['username']}' est-il configuré ?</li>";
        echo "<li><strong>Permissions :</strong> L'utilisateur a-t-il les droits de connexion ?</li>";
        echo "<li><strong>Version :</strong> Quelle version de HFSQL utilisez-vous ?</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";

        // Informations système
        echo "<div class='test-section'>";
        echo "<h2>ℹ️ Informations système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP :</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture PHP :</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>OS :</strong> " . PHP_OS . "</li>";
        echo "<li><strong>SAPI :</strong> " . php_sapi_name() . "</li>";
        echo "<li><strong>Extensions ODBC :</strong> " . (extension_loaded('odbc') ? 'ODBC ✅' : 'ODBC ❌') . ", " . (extension_loaded('pdo_odbc') ? 'PDO_ODBC ✅' : 'PDO_ODBC ❌') . "</li>";
        echo "<li><strong>Date/Heure :</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer le diagnostic</a>
                <a href="creer_dsn_systeme.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔧 Créer DSN système</a>
            </p>
        </div>
    </div>
</body>
</html>
