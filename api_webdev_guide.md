# Guide Création API WebDev pour DataCafe

## Étape 1 : <PERSON><PERSON>er le projet WebDev

1. **Ouvrez WebDev**
2. **Nouveau projet** → Site WebDev → Site dynamique
3. **Nom :** `API_DataCafe`
4. **Importez votre analyse** HFSQL existante (DataCafe)

## Étape 2 : Créer la page API categories.awp

Créez une page AWP nommée `categories.awp` avec ce code :

```windev
<%
// categories.awp - API pour récupérer les catégories

// En-têtes pour API REST
PageAffiche("")
HTTPCodeRéponse(200)
HTTPEntête("Content-Type: application/json; charset=utf-8")
HTTPEntête("Access-Control-Allow-Origin: *")
HTTPEntête("Access-Control-Allow-Methods: GET, POST, OPTIONS")
HTTPEntête("Access-Control-Allow-Headers: Content-Type")

// Gestion CORS
SI PageParamètre("REQUEST_METHOD") = "OPTIONS" ALORS
    RETOUR
FIN

// Construction du JSON
sJSON est une chaîne = "{"
sJSON += """error"": false,"
sJSON += """data"": ["

bPremier est un booléen = Vrai

// POUR TOUT Categorie (votre table réelle)
POUR TOUT Categorie
    SI PAS bPremier ALORS
        sJSON += ","
    FIN
    
    // Échapper les guillemets dans les valeurs
    sNom est une chaîne = Remplace(Categorie.categories, """", "\""")
    sPhoto est une chaîne = Remplace(Categorie.photo, """", "\""")
    
    sJSON += "{"
    sJSON += """IDCategorie"": " + Categorie.IDCategorie + ","
    sJSON += """categories"": """ + sNom + ""","
    sJSON += """photo"": """ + sPhoto + """"
    sJSON += "}"
    
    bPremier = Faux
FIN

sJSON += "]"
sJSON += "}"

// Retourner le JSON
ChaineConstruitUTF8(sJSON)
%>
```

## Étape 3 : Déployer et tester

1. **Déployez** le site sur votre serveur WebDev local
2. **Configurez** le port (ex: 8080)
3. **Testez** l'URL : `http://localhost:8080/categories.awp`

## Étape 4 : Vérifier le résultat

L'API doit retourner un JSON comme :
```json
{
    "error": false,
    "data": [
        {
            "IDCategorie": 1,
            "categories": "Nom réel de votre catégorie",
            "photo": "chemin/vers/photo.jpg"
        }
    ]
}
```

## Étape 5 : Connecter PHP

Une fois l'API créée, votre PHP récupérera automatiquement les vraies données !
