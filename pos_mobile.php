<?php
require_once 'pos_config.php';

// Gestion des actions AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];
    
    switch ($action) {
        case 'add_to_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity'] ?? 1);
            
            if ($pos->addToCart($articleId, $quantity)) {
                $response['success'] = true;
                $response['cart_count'] = count($pos->getCart());
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
            } else {
                $response['message'] = 'Stock insuffisant';
            }
            break;
            
        case 'update_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity']);
            
            if ($pos->updateCartQuantity($articleId, $quantity)) {
                $response['success'] = true;
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
            }
            break;
            
        case 'remove_from_cart':
            $articleId = intval($_POST['article_id']);
            
            if ($pos->removeFromCart($articleId)) {
                $response['success'] = true;
                $response['cart_count'] = count($pos->getCart());
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
            }
            break;
            
        case 'process_order':
            $paymentMethod = $_POST['payment_method'] ?? 'cash';
            
            $orderId = $pos->processOrder($paymentMethod);
            if ($orderId) {
                $response['success'] = true;
                $response['order_id'] = $orderId;
                $response['message'] = 'Commande validée !';
            } else {
                $response['message'] = 'Erreur lors de la validation';
            }
            break;
    }
    
    echo json_encode($response);
    exit;
}

// Récupération des données pour l'affichage
$categories = $pos->getCategories();
$selectedCategory = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

if ($searchQuery) {
    $articles = $pos->searchArticles($searchQuery);
} else {
    $articles = $pos->getArticlesByCategory($selectedCategory ?: null);
}

$cart = $pos->getCart();
$cartTotal = $pos->getCartTotalTTC();
$todayStats = $pos->getTodayStats();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo POS_NAME; ?> - Point de Vente</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="BeCoffe POS">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #666;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-weight: 600;
            color: #2c3e50;
            display: block;
        }
        
        /* Search Bar */
        .search-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            outline: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .search-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            font-size: 18px;
            cursor: pointer;
        }
        
        /* Categories */
        .categories-container {
            padding: 0 20px 20px;
        }
        
        .categories-scroll {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 10px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .categories-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-btn {
            min-width: 120px;
            padding: 12px 20px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .category-btn.active,
        .category-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            transform: translateY(-2px);
        }
        
        /* Main Content */
        .main-content {
            display: flex;
            gap: 20px;
            padding: 0 20px 20px;
            min-height: calc(100vh - 200px);
        }
        
        /* Articles Grid */
        .articles-section {
            flex: 2;
        }
        
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .article-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .article-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .article-icon {
            font-size: 40px;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .article-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .article-category {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .article-stock {
            font-size: 12px;
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .article-price {
            font-size: 18px;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 15px;
        }
        
        .add-btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .add-btn:hover {
            transform: scale(1.05);
        }
        
        /* Cart Section */
        .cart-section {
            flex: 1;
            min-width: 350px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .cart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .cart-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .cart-count {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .cart-items {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .cart-item-info {
            flex: 1;
        }
        
        .cart-item-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .cart-item-price {
            color: #666;
            font-size: 14px;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .qty-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #f0f0f0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .qty-btn:hover {
            background: #667eea;
            color: white;
        }
        
        .quantity {
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }
        
        .cart-total {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .total-final {
            font-size: 20px;
            font-weight: 700;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 10px;
        }
        
        .payment-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .payment-btn {
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-cash {
            background: #28a745;
            color: white;
        }
        
        .payment-card {
            background: #007bff;
            color: white;
        }
        
        .payment-btn:hover {
            transform: scale(1.05);
        }
        
        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .empty-icon {
            font-size: 60px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .cart-section {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                border-radius: 15px 15px 0 0;
                max-height: 50vh;
                z-index: 200;
                transform: translateY(calc(100% - 80px));
                transition: transform 0.3s ease;
            }
            
            .cart-section.expanded {
                transform: translateY(0);
            }
            
            .cart-toggle {
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(255, 255, 255, 0.95);
                border: none;
                border-radius: 20px 20px 0 0;
                padding: 10px 30px;
                cursor: pointer;
            }
            
            .articles-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 10px;
            }
            
            .stats {
                display: none;
            }
        }
        
        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Success Animation */
        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: successPop 0.5s ease-out;
        }
        
        @keyframes successPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-coffee"></i>
                <?php echo POS_NAME; ?>
            </div>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-value"><?php echo $todayStats['orders_count']; ?></span>
                    <span>Commandes</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo formatPrice($todayStats['total_revenue']); ?></span>
                    <span>CA Jour</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo $todayStats['items_sold']; ?></span>
                    <span>Articles</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="search-container">
        <div class="search-box">
            <input type="text" class="search-input" placeholder="Rechercher un article..." 
                   value="<?php echo htmlspecialchars($searchQuery); ?>" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- Categories -->
    <div class="categories-container">
        <div class="categories-scroll">
            <button class="category-btn <?php echo empty($selectedCategory) ? 'active' : ''; ?>" 
                    onclick="selectCategory('')">
                <i class="fas fa-th-large"></i> Tout
            </button>
            <?php foreach ($categories as $category): ?>
                <button class="category-btn <?php echo $selectedCategory == $category['IDCategorie'] ? 'active' : ''; ?>" 
                        onclick="selectCategory(<?php echo $category['IDCategorie']; ?>)">
                    <?php echo htmlspecialchars($category['categories']); ?>
                </button>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Articles Section -->
        <div class="articles-section">
            <?php if (empty($articles)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Aucun article trouvé</h3>
                    <p>Essayez de modifier votre recherche ou sélectionnez une autre catégorie</p>
                </div>
            <?php else: ?>
                <div class="articles-grid">
                    <?php foreach ($articles as $article): ?>
                        <div class="article-card" onclick="addToCart(<?php echo $article['IDarticles']; ?>)">
                            <div class="article-icon">
                                <?php
                                $icon = '🍽️';
                                $name = strtolower($article['designation']);
                                if (strpos($name, 'café') !== false || strpos($name, 'coffee') !== false) $icon = '☕';
                                elseif (strpos($name, 'thé') !== false || strpos($name, 'tea') !== false) $icon = '🍵';
                                elseif (strpos($name, 'sandwich') !== false) $icon = '🥪';
                                elseif (strpos($name, 'salade') !== false) $icon = '🥗';
                                elseif (strpos($name, 'dessert') !== false || strpos($name, 'gâteau') !== false) $icon = '🍰';
                                elseif (strpos($name, 'glace') !== false) $icon = '🍦';
                                echo $icon;
                                ?>
                            </div>
                            <div class="article-name"><?php echo htmlspecialchars($article['designation']); ?></div>
                            <div class="article-category"><?php echo htmlspecialchars($article['nom_categorie']); ?></div>
                            <div class="article-stock">Stock: <?php echo $article['quantite']; ?></div>
                            <div class="article-price"><?php echo formatPrice($pos->getArticlePrice($article['IDarticles'])); ?></div>
                            <button class="add-btn">
                                <i class="fas fa-plus"></i> Ajouter
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Cart Section -->
        <div class="cart-section" id="cartSection">
            <button class="cart-toggle" onclick="toggleCart()" style="display: none;">
                <i class="fas fa-shopping-cart"></i>
                <span id="mobileCartCount"><?php echo count($cart); ?></span>
            </button>
            
            <div class="cart-header">
                <div class="cart-title">
                    <i class="fas fa-shopping-cart"></i> Commande
                </div>
                <div class="cart-count" id="cartCount"><?php echo count($cart); ?></div>
            </div>

            <div class="cart-items" id="cartItems">
                <?php if (empty($cart)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <p>Panier vide</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($cart as $articleId => $item): ?>
                        <div class="cart-item" data-article-id="<?php echo $articleId; ?>">
                            <div class="cart-item-info">
                                <div class="cart-item-name"><?php echo htmlspecialchars($item['article']['designation']); ?></div>
                                <div class="cart-item-price"><?php echo formatPrice($item['price']); ?> × <?php echo $item['quantity']; ?></div>
                            </div>
                            <div class="quantity-controls">
                                <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] - 1; ?>)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity"><?php echo $item['quantity']; ?></span>
                                <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] + 1; ?>)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <?php if (!empty($cart)): ?>
                <div class="cart-total">
                    <div class="total-row">
                        <span>Sous-total HT:</span>
                        <span><?php echo formatPrice($pos->getCartTotal()); ?></span>
                    </div>
                    <div class="total-row">
                        <span>TVA (<?php echo (POS_TAX_RATE * 100); ?>%):</span>
                        <span><?php echo formatPrice($pos->getCartTax()); ?></span>
                    </div>
                    <div class="total-row total-final">
                        <span>Total TTC:</span>
                        <span id="cartTotal"><?php echo formatPrice($cartTotal); ?></span>
                    </div>
                </div>

                <div class="payment-buttons">
                    <button class="payment-btn payment-cash" onclick="processPayment('cash')">
                        <i class="fas fa-money-bill-wave"></i><br>
                        Espèces
                    </button>
                    <button class="payment-btn payment-card" onclick="processPayment('card')">
                        <i class="fas fa-credit-card"></i><br>
                        Carte
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Variables globales
        let isLoading = false;
        
        // Fonctions de navigation
        function selectCategory(categoryId) {
            window.location.href = `?category=${categoryId}`;
        }
        
        function performSearch() {
            const searchValue = document.getElementById('searchInput').value;
            window.location.href = `?search=${encodeURIComponent(searchValue)}`;
        }
        
        // Gestion du panier
        function addToCart(articleId, quantity = 1) {
            if (isLoading) return;
            
            isLoading = true;
            const btn = event.target.closest('.article-card').querySelector('.add-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div>';
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add_to_cart&article_id=${articleId}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCartDisplay();
                    showSuccess('Article ajouté !');
                } else {
                    showError(data.message || 'Erreur lors de l\'ajout');
                }
            })
            .catch(error => {
                showError('Erreur de connexion');
            })
            .finally(() => {
                isLoading = false;
                btn.innerHTML = originalText;
            });
        }
        
        function updateQuantity(articleId, newQuantity) {
            if (isLoading) return;
            
            isLoading = true;
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_cart&article_id=${articleId}&quantity=${newQuantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCartDisplay();
                } else {
                    showError('Erreur lors de la mise à jour');
                }
            })
            .finally(() => {
                isLoading = false;
            });
        }
        
        function processPayment(method) {
            if (isLoading) return;
            
            if (!confirm('Confirmer la commande ?')) return;
            
            isLoading = true;
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=process_order&payment_method=${method}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(`Commande ${data.order_id} validée !`);
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showError(data.message || 'Erreur lors de la validation');
                }
            })
            .finally(() => {
                isLoading = false;
            });
        }
        
        function updateCartDisplay() {
            // Recharger la page pour mettre à jour l'affichage du panier
            // Dans une version plus avancée, on pourrait mettre à jour via AJAX
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }
        
        // Fonctions utilitaires
        function showSuccess(message) {
            const div = document.createElement('div');
            div.className = 'success-animation';
            div.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            document.body.appendChild(div);
            
            setTimeout(() => {
                div.remove();
            }, 3000);
        }
        
        function showError(message) {
            alert(message); // Remplacer par une notification plus élégante
        }
        
        // Gestion mobile
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.toggle('expanded');
        }
        
        // Gestion responsive
        function handleResize() {
            const cartToggle = document.querySelector('.cart-toggle');
            if (window.innerWidth <= 768) {
                cartToggle.style.display = 'block';
            } else {
                cartToggle.style.display = 'none';
                document.getElementById('cartSection').classList.remove('expanded');
            }
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            handleResize();
            
            // Recherche en temps réel
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        performSearch();
                    }
                }, 500);
            });
            
            // Gestion du clavier
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
        
        window.addEventListener('resize', handleResize);
        
        // PWA - Service Worker (optionnel)
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('sw.js').catch(console.error);
        }
    </script>
</body>
</html>
