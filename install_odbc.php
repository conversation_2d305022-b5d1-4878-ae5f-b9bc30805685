<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation Extension ODBC PHP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .code { background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; margin: 10px 0; border-left: 4px solid #007bff; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 4px; border-left: 4px solid #28a745; }
        h1, h2, h3 { color: #333; }
        ol li { margin: 10px 0; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background-color: #e9ecef; border: 1px solid #ddd; cursor: pointer; margin-right: 5px; }
        .tab.active { background-color: #007bff; color: white; }
    </style>
    <script>
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            var contents = document.getElementsByClassName('tab-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }
            
            // Désactiver tous les onglets
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Activer l'onglet et le contenu sélectionnés
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</head>
<body>
    <div class="container">
        <h1>🔧 Installation de l'Extension ODBC PHP</h1>
        
        <?php
        // Informations sur l'environnement PHP
        $php_version = phpversion();
        $php_sapi = php_sapi_name();
        $os = php_uname('s');
        $architecture = php_uname('m');
        $php_ini_path = php_ini_loaded_file();
        $extensions_dir = ini_get('extension_dir');
        
        echo "<div class='info'>";
        echo "<h2>📋 Informations sur votre environnement</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP:</strong> $php_version</li>";
        echo "<li><strong>SAPI:</strong> $php_sapi</li>";
        echo "<li><strong>Système:</strong> $os ($architecture)</li>";
        echo "<li><strong>Fichier php.ini:</strong> " . ($php_ini_path ?: 'Non trouvé') . "</li>";
        echo "<li><strong>Répertoire extensions:</strong> $extensions_dir</li>";
        echo "</ul>";
        echo "</div>";
        
        // Vérifier si ODBC est déjà installé
        if (extension_loaded('odbc')) {
            echo "<div class='success'>";
            echo "<h2>✅ Extension ODBC déjà installée</h2>";
            echo "<p>Version ODBC: " . phpversion('odbc') . "</p>";
            echo "<p><a href='index.php'>🔙 Retour au test de connexion HFSQL</a></p>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Extension ODBC non installée</h2>";
            echo "<p>Vous devez installer l'extension ODBC pour vous connecter à HFSQL.</p>";
            echo "</div>";
        }
        
        // Vérifier les extensions disponibles
        $loaded_extensions = get_loaded_extensions();
        $database_extensions = array_filter($loaded_extensions, function($ext) {
            return in_array(strtolower($ext), ['pdo', 'mysqli', 'pgsql', 'sqlite3', 'odbc', 'pdo_odbc']);
        });
        
        if (!empty($database_extensions)) {
            echo "<div class='info'>";
            echo "<h3>🗄️ Extensions de base de données disponibles:</h3>";
            echo "<ul>";
            foreach ($database_extensions as $ext) {
                echo "<li>$ext</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        ?>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('windows')">Windows</div>
            <div class="tab" onclick="showTab('linux')">Linux</div>
            <div class="tab" onclick="showTab('xampp')">XAMPP</div>
            <div class="tab" onclick="showTab('wamp')">WAMP</div>
        </div>
        
        <!-- Instructions pour Windows -->
        <div id="windows" class="tab-content active">
            <h2>🪟 Installation sur Windows</h2>
            
            <div class="step">
                <h3>Étape 1: Vérifier votre installation PHP</h3>
                <p>Déterminez si vous utilisez PHP via :</p>
                <ul>
                    <li>XAMPP, WAMP, ou autre stack</li>
                    <li>Installation PHP standalone</li>
                    <li>Serveur web intégré (IIS avec PHP)</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Étape 2: Activer l'extension ODBC</h3>
                <p><strong>Méthode 1 - Modifier php.ini :</strong></p>
                <ol>
                    <li>Localisez votre fichier php.ini : <code><?php echo $php_ini_path ?: 'Utilisez phpinfo() pour le trouver'; ?></code></li>
                    <li>Ouvrez le fichier avec un éditeur de texte (en tant qu'administrateur)</li>
                    <li>Recherchez la ligne : <code>;extension=odbc</code></li>
                    <li>Supprimez le point-virgule pour la décommenter : <code>extension=odbc</code></li>
                    <li>Sauvegardez le fichier</li>
                    <li>Redémarrez votre serveur web</li>
                </ol>
                
                <p><strong>Méthode 2 - Si l'extension n'existe pas :</strong></p>
                <ol>
                    <li>Téléchargez l'extension ODBC compatible avec votre version PHP</li>
                    <li>Copiez le fichier .dll dans le répertoire des extensions : <code><?php echo $extensions_dir; ?></code></li>
                    <li>Ajoutez la ligne dans php.ini : <code>extension=php_odbc.dll</code></li>
                    <li>Redémarrez le serveur web</li>
                </ol>
            </div>
            
            <div class="step">
                <h3>Étape 3: Installer le driver HFSQL ODBC</h3>
                <ol>
                    <li>Téléchargez le driver HFSQL ODBC depuis le site PC SOFT</li>
                    <li>Installez le driver en tant qu'administrateur</li>
                    <li>Vérifiez l'installation via "Sources de données ODBC" dans Windows</li>
                </ol>
            </div>
        </div>
        
        <!-- Instructions pour Linux -->
        <div id="linux" class="tab-content">
            <h2>🐧 Installation sur Linux</h2>
            
            <div class="step">
                <h3>Ubuntu/Debian :</h3>
                <div class="code">
                    sudo apt update<br>
                    sudo apt install php-odbc<br>
                    sudo systemctl restart apache2
                </div>
            </div>
            
            <div class="step">
                <h3>CentOS/RHEL/Fedora :</h3>
                <div class="code">
                    sudo yum install php-odbc<br>
                    # ou pour les versions récentes :<br>
                    sudo dnf install php-odbc<br>
                    sudo systemctl restart httpd
                </div>
            </div>
            
            <div class="step">
                <h3>Installation du driver HFSQL :</h3>
                <p>Contactez PC SOFT pour obtenir le driver HFSQL ODBC pour Linux, ou utilisez une alternative comme FreeTDS si compatible.</p>
            </div>
        </div>
        
        <!-- Instructions pour XAMPP -->
        <div id="xampp" class="tab-content">
            <h2>🔶 XAMPP</h2>
            
            <div class="step">
                <h3>Activation de l'extension ODBC :</h3>
                <ol>
                    <li>Ouvrez le panneau de contrôle XAMPP</li>
                    <li>Cliquez sur "Config" à côté d'Apache</li>
                    <li>Sélectionnez "PHP (php.ini)"</li>
                    <li>Recherchez <code>;extension=odbc</code></li>
                    <li>Supprimez le point-virgule : <code>extension=odbc</code></li>
                    <li>Sauvegardez et redémarrez Apache</li>
                </ol>
            </div>
            
            <div class="warning">
                <strong>Note :</strong> XAMPP inclut généralement l'extension ODBC, il suffit de l'activer.
            </div>
        </div>
        
        <!-- Instructions pour WAMP -->
        <div id="wamp" class="tab-content">
            <h2>🟢 WAMP</h2>
            
            <div class="step">
                <h3>Activation via l'interface WAMP :</h3>
                <ol>
                    <li>Clic droit sur l'icône WAMP dans la barre des tâches</li>
                    <li>PHP → Extensions PHP</li>
                    <li>Cochez "php_odbc"</li>
                    <li>Redémarrez tous les services</li>
                </ol>
            </div>
            
            <div class="step">
                <h3>Activation manuelle :</h3>
                <ol>
                    <li>Ouvrez le fichier php.ini via WAMP</li>
                    <li>Recherchez <code>;extension=php_odbc</code></li>
                    <li>Supprimez le point-virgule</li>
                    <li>Redémarrez les services</li>
                </ol>
            </div>
        </div>
        
        <div class="info">
            <h2>🔍 Vérification de l'installation</h2>
            <p>Après avoir suivi les étapes :</p>
            <ol>
                <li><a href="<?php echo $_SERVER['PHP_SELF']; ?>">🔄 Rechargez cette page</a> pour vérifier si ODBC est maintenant disponible</li>
                <li>Si ODBC est installé, <a href="index.php">🔙 retournez au test de connexion HFSQL</a></li>
            </ol>
        </div>
        
        <div class="warning">
            <h2>⚠️ Problèmes courants</h2>
            <ul>
                <li><strong>Extension non trouvée :</strong> Vérifiez que le fichier php_odbc.dll existe dans le répertoire des extensions</li>
                <li><strong>Erreur de chargement :</strong> Vérifiez la compatibilité de version (32-bit vs 64-bit)</li>
                <li><strong>Permissions :</strong> Assurez-vous d'avoir les droits administrateur</li>
                <li><strong>Redémarrage :</strong> N'oubliez pas de redémarrer le serveur web après modification</li>
            </ul>
        </div>
        
        <div class="step">
            <h2>📞 Support supplémentaire</h2>
            <p>Si vous rencontrez des difficultés :</p>
            <ul>
                <li>Vérifiez la documentation de votre stack (XAMPP, WAMP, etc.)</li>
                <li>Consultez les logs d'erreur PHP</li>
                <li>Contactez PC SOFT pour le support du driver HFSQL ODBC</li>
            </ul>
        </div>
    </div>
</body>
</html>
