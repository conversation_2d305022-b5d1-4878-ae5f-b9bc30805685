<?php
// Test de vérification de la solution PDO
echo "<!DOCTYPE html>
<html>
<head>
    <title>Vérification Connexion PDO - DataCafe</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #28a745; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #17a2b8; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; border: 1px solid #e9ecef; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .metric { display: inline-block; margin: 10px 15px 10px 0; padding: 8px 12px; background: #e9ecef; border-radius: 4px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🎉 Vérification de votre solution PDO</h1>";

// Configuration identique à test1.php
$dsn = "odbc:DataCafe";
$username = "admin";
$password = "";

echo "<div class='info'>";
echo "<h2>📋 Configuration testée</h2>";
echo "<div class='code'>";
echo "DSN: $dsn<br>";
echo "Username: $username<br>";
echo "Password: " . ($password ? "[défini]" : "[vide]");
echo "</div>";
echo "</div>";

$start_time = microtime(true);

try {
    // Test exact de votre code
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $connection_time = round((microtime(true) - $start_time) * 1000, 2);
    
    echo "<div class='success'>";
    echo "<h2>✅ Connexion réussie !</h2>";
    echo "<div class='metric'>Temps: {$connection_time}ms</div>";
    echo "<div class='metric'>Status: Connecté</div>";
    echo "<div class='metric'>Driver: PDO ODBC</div>";
    echo "</div>";
    
    // Tests supplémentaires pour valider la connexion
    echo "<h2>🧪 Tests de validation</h2>";
    echo "<table>";
    echo "<tr><th>Test</th><th>Résultat</th><th>Détails</th></tr>";
    
    // Test 1: Requête simple
    try {
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<tr><td>Requête SELECT</td><td class='success'>✅ Réussie</td><td>Résultat: " . json_encode($result) . "</td></tr>";
    } catch (Exception $e) {
        echo "<tr><td>Requête SELECT</td><td>⚠️ Échouée</td><td>" . htmlspecialchars($e->getMessage()) . "</td></tr>";
    }
    
    // Test 2: Informations de connexion
    try {
        $driver_info = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
        echo "<tr><td>Driver PDO</td><td class='success'>✅ Détecté</td><td>$driver_info</td></tr>";
    } catch (Exception $e) {
        echo "<tr><td>Driver PDO</td><td>⚠️ Non disponible</td><td>-</td></tr>";
    }
    
    // Test 3: Version du serveur
    try {
        $stmt = $pdo->query("SELECT @@VERSION as version");
        $version = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<tr><td>Version serveur</td><td class='success'>✅ Obtenue</td><td>" . ($version['version'] ?? 'N/A') . "</td></tr>";
    } catch (Exception $e) {
        echo "<tr><td>Version serveur</td><td>⚠️ Non disponible</td><td>Requête non supportée</td></tr>";
    }
    
    echo "</table>";
    
    // Affichage du code validé
    echo "<div class='success'>";
    echo "<h2>✅ Votre code fonctionne parfaitement !</h2>";
    echo "<p>La connexion PDO à votre base HFSQL DataCafe est opérationnelle.</p>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>💻 Code validé</h3>";
    echo "<div class='code'>";
    echo htmlspecialchars('<?php
$dsn = "odbc:DataCafe";
$username = "admin";
$password = "";

try {
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connexion réussie !";
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}
?>');
    echo "</div>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🚀 Prochaines étapes</h3>";
    echo "<ul>";
    echo "<li>Vous pouvez maintenant exécuter des requêtes SQL sur votre base DataCafe</li>";
    echo "<li>Utilisez <code>\$pdo->query()</code> pour les SELECT</li>";
    echo "<li>Utilisez <code>\$pdo->prepare()</code> pour les requêtes avec paramètres</li>";
    echo "<li>Exemple: <code>\$stmt = \$pdo->query('SELECT * FROM ma_table');</code></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    $connection_time = round((microtime(true) - $start_time) * 1000, 2);
    
    echo "<div class='error'>";
    echo "<h2>❌ Erreur de connexion</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Temps écoulé:</strong> {$connection_time}ms</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>