<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Équivalent PHP - Catégories HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        
        /* Équivalent de la Zone Répétée */
        .zone-repetee {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .ligne-categorie {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .ligne-categorie:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .photo-categorie {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .nom-categorie {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .id-categorie {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
        }
        
        .compteur {
            background: #007bff;
            color: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
        }
        
        .no-photo {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            border-radius: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📂 Équivalent PHP - Catégories HFSQL</h1>
        
        <div class="info">
            <h3>Code WinDev original :</h3>
            <code>
                POUR TOUT Categorie<br>
                &nbsp;&nbsp;&nbsp;&nbsp;ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)<br>
                FIN
            </code>
        </div>

        <?php
        // Classe pour simuler l'accès aux données HFSQL
        class CategorieManager {
            private $categories = [];
            
            public function __construct() {
                // Simulation des données (remplacez par votre API ou base de données)
                $this->categories = [
                    ['IDCategorie' => 1, 'categories' => 'Boissons chaudes', 'photo' => 'images/boissons_chaudes.jpg'],
                    ['IDCategorie' => 2, 'categories' => 'Boissons froides', 'photo' => 'images/boissons_froides.jpg'],
                    ['IDCategorie' => 3, 'categories' => 'Pâtisseries', 'photo' => 'images/patisseries.jpg'],
                    ['IDCategorie' => 4, 'categories' => 'Sandwichs', 'photo' => 'images/sandwichs.jpg'],
                    ['IDCategorie' => 5, 'categories' => 'Salades', 'photo' => 'images/salades.jpg'],
                    ['IDCategorie' => 6, 'categories' => 'Desserts', 'photo' => ''],
                ];
            }
            
            // Équivalent de "POUR TOUT Categorie"
            public function pourToutCategorie() {
                return $this->categories;
            }
            
            // Méthode pour charger depuis une API (désactivée temporairement)
            public function chargerDepuisAPI($apiUrl = 'http://localhost:8080/api/categories.awp') {
                // API WebDev pas encore créée - retourne false pour utiliser les données de simulation
                return false;

                /* Code pour plus tard quand l'API sera prête :
                try {
                    $context = stream_context_create([
                        'http' => [
                            'method' => 'GET',
                            'header' => 'Accept: application/json',
                            'timeout' => 10
                        ]
                    ]);

                    $response = file_get_contents($apiUrl, false, $context);

                    if ($response !== false) {
                        $data = json_decode($response, true);
                        if (!$data['error']) {
                            $this->categories = $data['data'];
                            return true;
                        }
                    }
                } catch (Exception $e) {
                    // En cas d'erreur, on garde les données de simulation
                }
                return false;
                */
            }
        }

        // Classe pour simuler la Zone Répétée
        class ZoneRepetee {
            private $lignes = [];
            
            // Équivalent de ZoneRépétéeSupprimeTout
            public function supprimeTout() {
                $this->lignes = [];
            }
            
            // Équivalent de ZoneRépétéeAjouteLigne
            public function ajouteLigne($nom, $photo, $id) {
                $this->lignes[] = [
                    'nom' => $nom,
                    'photo' => $photo,
                    'id' => $id
                ];
            }
            
            // Équivalent de ZoneRépétéeOccurrence
            public function occurrence() {
                return count($this->lignes);
            }
            
            // Méthode pour afficher la zone répétée
            public function afficher() {
                if (empty($this->lignes)) {
                    echo "<div class='error'>Aucune catégorie à afficher</div>";
                    return;
                }
                
                echo "<div class='zone-repetee'>";
                foreach ($this->lignes as $ligne) {
                    echo "<div class='ligne-categorie'>";
                    
                    // Affichage de la photo
                    if (!empty($ligne['photo']) && file_exists($ligne['photo'])) {
                        echo "<img src='" . htmlspecialchars($ligne['photo']) . "' alt='Photo catégorie' class='photo-categorie'>";
                    } else {
                        echo "<div class='no-photo'>📷 Pas de photo</div>";
                    }
                    
                    // Affichage du nom
                    echo "<div class='nom-categorie'>" . htmlspecialchars($ligne['nom']) . "</div>";
                    
                    // Affichage de l'ID
                    echo "<div class='id-categorie'>ID: " . htmlspecialchars($ligne['id']) . "</div>";
                    
                    echo "</div>";
                }
                echo "</div>";
            }
        }

        // === EXÉCUTION DU CODE ÉQUIVALENT ===
        
        echo "<h2>🔄 Exécution du code équivalent</h2>";
        
        // Initialisation
        $categorieManager = new CategorieManager();
        $ZR_cat = new ZoneRepetee();
        
        // Mode simulation (API WebDev pas encore créée)
        echo "<div class='info'>🔧 Mode simulation - API WebDev en cours de développement</div>";
        echo "<div class='success'>✅ Utilisation des données de simulation pour démonstration</div>";
        
        // Vider la zone répétée (équivalent de ZoneRépétéeSupprimeTout)
        $ZR_cat->supprimeTout();
        
        // POUR TOUT Categorie (équivalent exact)
        echo "<div class='info'>Exécution de : <strong>POUR TOUT Categorie</strong></div>";
        
        $compteur = 0;
        foreach ($categorieManager->pourToutCategorie() as $categorie) {
            // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
            $ZR_cat->ajouteLigne(
                $categorie['categories'],    // Categorie.categories
                $categorie['photo'],         // Categorie.photo  
                $categorie['IDCategorie']    // Categorie.IDCategorie
            );
            $compteur++;
        }
        
        // Affichage du résultat
        echo "<div class='compteur'>";
        echo "📊 Nombre de catégories chargées : " . $ZR_cat->occurrence();
        echo "</div>";
        
        echo "<h2>📂 Zone Répétée - Catégories</h2>";
        $ZR_cat->afficher();
        ?>

        <div style="margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
            <h3>💻 Code PHP équivalent :</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 5px; overflow-x: auto;"><code><?php echo htmlspecialchars('
// Équivalent exact du code WinDev
$ZR_cat = new ZoneRepetee();
$ZR_cat->supprimeTout();

// POUR TOUT Categorie
foreach ($categorieManager->pourToutCategorie() as $categorie) {
    // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
    $ZR_cat->ajouteLigne(
        $categorie["categories"],
        $categorie["photo"], 
        $categorie["IDCategorie"]
    );
}

// Affichage
$ZR_cat->afficher();
echo "Nombre: " . $ZR_cat->occurrence();
'); ?></code></pre>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>🔄 Actions</h3>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="test_api_complete.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🧪 Tester avec vraie API</a>
            </p>
        </div>
    </div>
</body>
</html>
