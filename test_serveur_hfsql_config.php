<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Configuration Serveur HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f8f9fa; }
        .command { background-color: #f8f9fa; padding: 8px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Configuration Serveur HFSQL</h1>

        <div class="info">
            <h2>🎯 Objectif</h2>
            <p>Tester différentes configurations de serveur et de port pour trouver la bonne combinaison ODBC.</p>
        </div>

        <?php
        // Configuration par défaut
        $default_config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Récupération des paramètres du formulaire
        $config = [
            'server' => $_POST['server'] ?? $default_config['server'],
            'port' => $_POST['port'] ?? $default_config['port'],
            'database' => $_POST['database'] ?? $default_config['database'],
            'username' => $_POST['username'] ?? $default_config['username'],
            'password' => $_POST['password'] ?? $default_config['password']
        ];

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_config'])) {
            echo "<div class='test-section'>";
            echo "<h2>🧪 Test de la configuration personnalisée</h2>";
            
            echo "<div class='info'>";
            echo "<h3>Configuration testée :</h3>";
            echo "<ul>";
            echo "<li><strong>Serveur :</strong> {$config['server']}</li>";
            echo "<li><strong>Port :</strong> {$config['port']}</li>";
            echo "<li><strong>Base :</strong> {$config['database']}</li>";
            echo "<li><strong>Utilisateur :</strong> {$config['username']}</li>";
            echo "</ul>";
            echo "</div>";
            
            // Test de connectivité réseau
            echo "<h3>1. Test de connectivité réseau</h3>";
            $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
            if ($socket) {
                fclose($socket);
                echo "<div class='success'>✅ Port {$config['port']} accessible sur {$config['server']}</div>";
            } else {
                echo "<div class='error'>❌ Port {$config['port']} inaccessible : $errstr ($errno)</div>";
            }
            
            // Test ODBC avec cette configuration
            echo "<h3>2. Test ODBC</h3>";
            if (extension_loaded('odbc')) {
                $test_dsns = [
                    'Standard' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}",
                    'Sans base' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}",
                    'Sans auth' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                    'Minimal' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']}"
                ];
                
                foreach ($test_dsns as $name => $dsn) {
                    echo "<h4>Test $name</h4>";
                    echo "<div class='command'>" . htmlspecialchars($dsn) . "</div>";
                    
                    $connection = @odbc_connect($dsn, $config['username'], $config['password']);
                    if ($connection) {
                        echo "<div class='success'>✅ Connexion réussie !</div>";
                        
                        // Test de requête
                        $result = @odbc_exec($connection, "SELECT COUNT(*) as nb FROM Categorie");
                        if ($result) {
                            $count = odbc_result($result, 1);
                            echo "<div class='success'>✅ Requête réussie : $count catégories trouvées</div>";
                            odbc_free_result($result);
                        } else {
                            echo "<div class='warning'>⚠️ Connexion OK mais requête échouée</div>";
                        }
                        
                        odbc_close($connection);
                        break; // Arrêter au premier succès
                    } else {
                        $error = odbc_errormsg();
                        echo "<div class='error'>❌ Échec : " . htmlspecialchars($error) . "</div>";
                    }
                }
            } else {
                echo "<div class='error'>❌ Extension ODBC non disponible</div>";
            }
            
            echo "</div>";
        }

        // Test automatique de différentes configurations
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['scan_configs'])) {
            echo "<div class='test-section'>";
            echo "<h2>🔍 Scan automatique des configurations</h2>";
            
            // Différentes combinaisons serveur/port à tester
            $test_configs = [
                ['server' => '127.0.0.1', 'port' => 4900, 'desc' => 'Configuration standard'],
                ['server' => 'localhost', 'port' => 4900, 'desc' => 'Localhost standard'],
                ['server' => '127.0.0.1', 'port' => 4901, 'desc' => 'Port alternatif 4901'],
                ['server' => '127.0.0.1', 'port' => 4902, 'desc' => 'Port alternatif 4902'],
                ['server' => '127.0.0.1', 'port' => 4999, 'desc' => 'Port alternatif 4999'],
                ['server' => '127.0.0.1', 'port' => 5000, 'desc' => 'Port alternatif 5000'],
                ['server' => '127.0.0.1', 'port' => 8080, 'desc' => 'Port web 8080'],
                ['server' => '127.0.0.1', 'port' => 1433, 'desc' => 'Port SQL Server'],
                ['server' => '0.0.0.0', 'port' => 4900, 'desc' => 'Toutes interfaces'],
            ];
            
            echo "<table>";
            echo "<tr><th>Configuration</th><th>Serveur:Port</th><th>Réseau</th><th>ODBC</th><th>Détails</th></tr>";
            
            foreach ($test_configs as $test_config) {
                echo "<tr>";
                echo "<td>{$test_config['desc']}</td>";
                echo "<td>{$test_config['server']}:{$test_config['port']}</td>";
                
                // Test réseau
                $socket = @fsockopen($test_config['server'], $test_config['port'], $errno, $errstr, 2);
                if ($socket) {
                    fclose($socket);
                    echo "<td style='background-color: #d4edda;'>✅ OK</td>";
                    
                    // Test ODBC si le réseau fonctionne
                    if (extension_loaded('odbc')) {
                        $dsn = "Driver={HFSQL};Server={$test_config['server']};Port={$test_config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";
                        $connection = @odbc_connect($dsn, $config['username'], $config['password']);
                        
                        if ($connection) {
                            echo "<td style='background-color: #d4edda;'>✅ SUCCÈS</td>";
                            echo "<td style='background-color: #d4edda;'>Connexion ODBC réussie !</td>";
                            odbc_close($connection);
                        } else {
                            $error = odbc_errormsg();
                            echo "<td style='background-color: #f8d7da;'>❌ Échec</td>";
                            echo "<td style='font-size: 10px;'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
                        }
                    } else {
                        echo "<td>-</td>";
                        echo "<td>ODBC non disponible</td>";
                    }
                } else {
                    echo "<td style='background-color: #f8d7da;'>❌ Fermé</td>";
                    echo "<td>-</td>";
                    echo "<td>$errstr</td>";
                }
                
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        }
        ?>

        <div class="test-section">
            <h2>⚙️ Configuration personnalisée</h2>
            <form method="post">
                <div class="form-group">
                    <label for="server">Serveur :</label>
                    <input type="text" id="server" name="server" value="<?php echo htmlspecialchars($config['server']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="port">Port :</label>
                    <input type="number" id="port" name="port" value="<?php echo htmlspecialchars($config['port']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="database">Base de données :</label>
                    <input type="text" id="database" name="database" value="<?php echo htmlspecialchars($config['database']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="username">Utilisateur :</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($config['username']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe :</label>
                    <input type="text" id="password" name="password" value="<?php echo htmlspecialchars($config['password']); ?>">
                </div>
                
                <div class="form-group">
                    <button type="submit" name="test_config">🧪 Tester cette configuration</button>
                    <button type="submit" name="scan_configs">🔍 Scanner toutes les configurations</button>
                </div>
            </form>
        </div>

        <div class="test-section">
            <h2>💡 Conseils de dépannage</h2>
            
            <div class="warning">
                <h3>🔍 Si aucune configuration ne fonctionne :</h3>
                <ol>
                    <li><strong>Vérifiez le Centre de Contrôle HFSQL :</strong>
                        <ul>
                            <li>Le serveur est-il démarré ?</li>
                            <li>Sur quel port écoute-t-il ?</li>
                            <li>L'accès ODBC est-il activé ?</li>
                        </ul>
                    </li>
                    
                    <li><strong>Vérifiez la configuration réseau :</strong>
                        <ul>
                            <li>Pare-feu Windows</li>
                            <li>Antivirus</li>
                            <li>Paramètres réseau</li>
                        </ul>
                    </li>
                    
                    <li><strong>Vérifiez le driver ODBC :</strong>
                        <ul>
                            <li>Version compatible</li>
                            <li>Architecture (32/64 bits)</li>
                            <li>Installation correcte</li>
                        </ul>
                    </li>
                </ol>
            </div>
            
            <div class="info">
                <h3>📋 Informations à noter :</h3>
                <ul>
                    <li><strong>Version PHP :</strong> <?php echo PHP_VERSION; ?> (<?php echo (PHP_INT_SIZE * 8); ?> bits)</li>
                    <li><strong>Extension ODBC :</strong> <?php echo extension_loaded('odbc') ? 'Chargée ✅' : 'Non chargée ❌'; ?></li>
                    <li><strong>OS :</strong> <?php echo PHP_OS; ?></li>
                </ul>
            </div>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="resolution_odbc_final.php" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔧 Diagnostic complet</a>
            </p>
        </div>
    </div>
</body>
</html>
