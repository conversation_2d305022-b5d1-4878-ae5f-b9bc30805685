<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catégories HFSQL - Version Finale</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        
        /* Zone Répétée optimisée */
        .zone-repetee {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .ligne-categorie {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .ligne-categorie:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.2);
            border-color: #007bff;
        }
        
        .photo-categorie {
            width: 100%;
            height: 160px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .no-photo {
            width: 100%;
            height: 160px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 32px;
        }
        
        .nom-categorie {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .id-categorie {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 11px;
            color: #666;
            background: rgba(255,255,255,0.9);
            padding: 3px 8px;
            border-radius: 12px;
            border: 1px solid #ddd;
        }
        
        .compteur {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            box-shadow: 0 2px 10px rgba(0,123,255,0.3);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .code-windev {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Catégories HFSQL - Version Finale Fonctionnelle</h1>
        
        <div class="success">
            <h3>✅ Connexion HFSQL réussie !</h3>
            <p>Utilisation de PDO avec DSN système : <code>odbc:DataCafe</code></p>
        </div>

        <div class="info">
            <h3>📋 Code WinDev équivalent :</h3>
            <div class="code-windev">
                POUR TOUT Categorie<br>
                &nbsp;&nbsp;&nbsp;&nbsp;ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)<br>
                FIN
            </div>
        </div>

        <?php
        // Configuration optimisée basée sur votre solution qui fonctionne
        $dsn = "odbc:DataCafe";
        $username = "admin";
        $password = "";

        try {
            // Connexion PDO avec options optimisées
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 30,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ]);
            
            echo "<div class='success'>✅ Connexion PDO établie avec succès</div>";
            
            // === ÉQUIVALENT EXACT DU CODE WINDEV ===
            
            echo "<div class='info'>";
            echo "<h2>🔄 Exécution : POUR TOUT Categorie</h2>";
            echo "</div>";
            
            // Requête équivalente à "POUR TOUT Categorie"
            $sql = "SELECT IDCategorie, categories, photo FROM Categorie ORDER BY IDCategorie";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            // Récupération de toutes les catégories (équivalent du parcours HFSQL)
            $categories = $stmt->fetchAll();
            
            // Statistiques
            $total_categories = count($categories);
            $avec_photo = 0;
            $sans_photo = 0;
            
            foreach ($categories as $cat) {
                if (!empty($cat['photo']) && $cat['photo'] !== '') {
                    $avec_photo++;
                } else {
                    $sans_photo++;
                }
            }
            
            // Affichage des statistiques
            echo "<div class='stats'>";
            echo "<div class='stat-card'>";
            echo "<div class='stat-number'>$total_categories</div>";
            echo "<div>Catégories totales</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-number'>$avec_photo</div>";
            echo "<div>Avec photo</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-number'>$sans_photo</div>";
            echo "<div>Sans photo</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-number'>" . date('H:i:s') . "</div>";
            echo "<div>Dernière mise à jour</div>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='compteur'>";
            echo "📊 Nombre de catégories chargées : $total_categories";
            echo "</div>";
            
            // === ZONE RÉPÉTÉE (équivalent de ZoneRépétéeAjouteLigne) ===
            
            echo "<h2>📂 Zone Répétée - Vraies Catégories HFSQL</h2>";
            
            if (empty($categories)) {
                echo "<div class='error'>Aucune catégorie trouvée dans la base DataCafe</div>";
            } else {
                echo "<div class='zone-repetee'>";
                
                foreach ($categories as $categorie) {
                    // Équivalent de ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
                    echo "<div class='ligne-categorie' onclick='afficherDetails({$categorie['IDCategorie']})'>";
                    
                    // ID en badge (coin supérieur droit)
                    echo "<div class='id-categorie'>ID: {$categorie['IDCategorie']}</div>";
                    
                    // Photo de la catégorie
                    if (!empty($categorie['photo']) && file_exists($categorie['photo'])) {
                        echo "<img src='" . htmlspecialchars($categorie['photo']) . "' alt='" . htmlspecialchars($categorie['categories']) . "' class='photo-categorie'>";
                    } else {
                        // Icône selon le type de catégorie
                        $icone = '📂';
                        $nom_lower = strtolower($categorie['categories']);
                        if (strpos($nom_lower, 'boisson') !== false) $icone = '☕';
                        elseif (strpos($nom_lower, 'café') !== false) $icone = '☕';
                        elseif (strpos($nom_lower, 'thé') !== false) $icone = '🍵';
                        elseif (strpos($nom_lower, 'pâtisserie') !== false) $icone = '🧁';
                        elseif (strpos($nom_lower, 'sandwich') !== false) $icone = '🥪';
                        elseif (strpos($nom_lower, 'salade') !== false) $icone = '🥗';
                        elseif (strpos($nom_lower, 'dessert') !== false) $icone = '🍰';
                        elseif (strpos($nom_lower, 'glace') !== false) $icone = '🍦';
                        elseif (strpos($nom_lower, 'snack') !== false) $icone = '🍿';
                        
                        echo "<div class='no-photo'>$icone</div>";
                    }
                    
                    // Nom de la catégorie
                    echo "<div class='nom-categorie'>" . htmlspecialchars($categorie['categories']) . "</div>";
                    
                    echo "</div>";
                }
                
                echo "</div>";
            }
            
            // Sauvegarde pour utilisation future
            file_put_contents('categories_success.json', json_encode([
                'timestamp' => date('Y-m-d H:i:s'),
                'source' => 'HFSQL_PDO_Success',
                'dsn' => $dsn,
                'count' => $total_categories,
                'data' => $categories
            ], JSON_PRETTY_PRINT));
            
            echo "<div class='success'>✅ Données sauvegardées dans 'categories_success.json' pour utilisation future</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Erreur de connexion PDO</h3>";
            echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><strong>Code :</strong> " . $e->getCode() . "</p>";
            echo "</div>";
            
            echo "<div class='info'>";
            echo "<h3>🔧 Vérifications nécessaires :</h3>";
            echo "<ul>";
            echo "<li>Le DSN système 'DataCafe' existe-t-il ?</li>";
            echo "<li>Le serveur HFSQL est-il démarré ?</li>";
            echo "<li>L'utilisateur 'admin' a-t-il les bonnes permissions ?</li>";
            echo "</ul>";
            echo "</div>";
        }
        ?>

        <script>
        function afficherDetails(id) {
            alert('Catégorie ID: ' + id + '\nCliquez pour voir les détails');
            // Ici vous pouvez ajouter une redirection vers une page de détails
            // window.location.href = 'details_categorie.php?id=' + id;
        }
        
        // Auto-refresh toutes les 5 minutes pour avoir les données à jour
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes
        </script>

        <div style="margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
            <h3>💻 Code PHP final optimisé :</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 5px; overflow-x: auto;"><code><?php echo htmlspecialchars('
// Votre solution qui fonctionne - optimisée
$dsn = "odbc:DataCafe";
$pdo = new PDO($dsn, "admin", "", [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
]);

// POUR TOUT Categorie (équivalent exact)
$sql = "SELECT IDCategorie, categories, photo FROM Categorie ORDER BY IDCategorie";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$categories = $stmt->fetchAll();

// ZoneRépétéeAjouteLigne équivalent
foreach ($categories as $categorie) {
    echo "<div class=\"categorie\">";
    echo "<h3>" . htmlspecialchars($categorie["categories"]) . "</h3>";
    echo "<span>ID: " . $categorie["IDCategorie"] . "</span>";
    echo "</div>";
}

echo "Nombre: " . count($categories);
'); ?></code></pre>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>🎯 Prochaines étapes</h3>
            <ol>
                <li><strong>✅ Catégories fonctionnelles</strong> - Votre solution marche parfaitement !</li>
                <li><strong>🔄 Adapter pour les articles</strong> - Même principe avec la table articles</li>
                <li><strong>🎨 Personnaliser l'affichage</strong> - Adapter selon vos besoins</li>
                <li><strong>🔗 Intégrer dans votre app</strong> - Utiliser ce code dans vos pages</li>
            </ol>
            
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="test1.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📝 Code original</a>
            </p>
        </div>
    </div>
</body>
</html>
