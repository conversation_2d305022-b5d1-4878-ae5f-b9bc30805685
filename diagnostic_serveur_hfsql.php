<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Serveur HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .critical { color: #721c24; background-color: #f5c6cb; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 5px solid #dc3545; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 11px; }
        .command { background-color: #e9ecef; padding: 8px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
        .solution { background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 5px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Diagnostic Serveur HFSQL</h1>

        <div class="critical">
            <h2>🚨 Problème identifié</h2>
            <p><strong>Erreur :</strong> "Chaîne de connexion insuffisante"</p>
            <p><strong>Cause probable :</strong> Le serveur HFSQL n'accepte pas les connexions ODBC ou n'est pas configuré correctement pour ODBC.</p>
        </div>

        <?php
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Test 1: Analyse détaillée du serveur HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔍 1. Analyse du serveur HFSQL</h2>";
        
        echo "<h3>Processus HFSQL détaillés</h3>";
        $detailed_processes = shell_exec('wmic process where "name like \'%hf%\' or name like \'%manta%\' or name like \'%windev%\'" get name,processid,commandline /format:csv 2>nul');
        if ($detailed_processes) {
            echo "<div class='success'>✅ Processus HFSQL trouvés :</div>";
            echo "<pre>" . htmlspecialchars($detailed_processes) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Aucun processus HFSQL détecté via WMIC</div>";
            
            // Fallback avec tasklist
            $tasklist_result = shell_exec('tasklist /v /fi "imagename eq *hf*" 2>nul');
            if (!$tasklist_result) {
                $tasklist_result = shell_exec('tasklist /v /fi "imagename eq *manta*" 2>nul');
            }
            if ($tasklist_result) {
                echo "<div class='info'>Processus via tasklist :</div>";
                echo "<pre>" . htmlspecialchars($tasklist_result) . "</pre>";
            }
        }
        
        echo "<h3>Services Windows HFSQL</h3>";
        $services = shell_exec('sc query | findstr /i hfsql 2>nul');
        if (!$services) {
            $services = shell_exec('sc query | findstr /i manta 2>nul');
        }
        if (!$services) {
            $services = shell_exec('sc query | findstr /i windev 2>nul');
        }
        
        if ($services) {
            echo "<div class='success'>✅ Services HFSQL trouvés :</div>";
            echo "<pre>" . htmlspecialchars($services) . "</pre>";
        } else {
            echo "<div class='warning'>⚠️ Aucun service HFSQL trouvé</div>";
        }
        echo "</div>";

        // Test 2: Test de communication réseau avancé
        echo "<div class='test-section'>";
        echo "<h2>🌐 2. Communication réseau avec le serveur</h2>";
        
        echo "<h3>Test de connexion socket brute</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 10);
        if ($socket) {
            echo "<div class='success'>✅ Connexion socket établie</div>";
            
            // Essayer de lire la bannière/réponse du serveur
            stream_set_timeout($socket, 3);
            $initial_response = @fread($socket, 1024);
            if ($initial_response) {
                echo "<div class='info'>Réponse initiale du serveur :</div>";
                echo "<pre>" . htmlspecialchars(substr($initial_response, 0, 500)) . "</pre>";
            } else {
                echo "<div class='warning'>⚠️ Aucune réponse initiale du serveur</div>";
            }
            
            // Essayer d'envoyer différentes commandes
            $test_commands = [
                "HELLO\r\n",
                "VERSION\r\n",
                "CONNECT\r\n",
                "INFO\r\n"
            ];
            
            foreach ($test_commands as $command) {
                echo "<h4>Test commande : " . htmlspecialchars(trim($command)) . "</h4>";
                @fwrite($socket, $command);
                usleep(500000); // Attendre 0.5 seconde
                $response = @fread($socket, 1024);
                if ($response) {
                    echo "<div class='info'>Réponse : " . htmlspecialchars(substr($response, 0, 200)) . "</div>";
                } else {
                    echo "<div class='warning'>⚠️ Aucune réponse</div>";
                }
            }
            
            fclose($socket);
        } else {
            echo "<div class='error'>❌ Impossible d'établir une connexion socket : $errstr ($errno)</div>";
        }
        
        echo "<h3>Analyse netstat détaillée</h3>";
        $netstat_detailed = shell_exec("netstat -ano | findstr :4900");
        if ($netstat_detailed) {
            echo "<div class='success'>✅ Port 4900 en écoute :</div>";
            echo "<pre>" . htmlspecialchars($netstat_detailed) . "</pre>";
            
            // Extraire le PID du processus
            $lines = explode("\n", trim($netstat_detailed));
            foreach ($lines as $line) {
                if (preg_match('/\s+(\d+)$/', $line, $matches)) {
                    $pid = $matches[1];
                    echo "<h4>Processus PID $pid :</h4>";
                    $process_info = shell_exec("tasklist /fi \"pid eq $pid\" /fo csv 2>nul");
                    if ($process_info) {
                        echo "<pre>" . htmlspecialchars($process_info) . "</pre>";
                    }
                }
            }
        } else {
            echo "<div class='error'>❌ Port 4900 non trouvé dans netstat</div>";
        }
        echo "</div>";

        // Test 3: Recherche de fichiers de configuration HFSQL
        echo "<div class='test-section'>";
        echo "<h2>📁 3. Configuration HFSQL</h2>";
        
        echo "<h3>Recherche de fichiers de configuration</h3>";
        $config_paths = [
            'C:\Program Files\PC SOFT',
            'C:\Program Files (x86)\PC SOFT',
            'C:\WinDev',
            'C:\HFSQL',
            'C:\ProgramData\PC SOFT',
            'C:\Users\<USER>\Documents\PC SOFT'
        ];
        
        $found_configs = [];
        foreach ($config_paths as $path) {
            if (is_dir($path)) {
                echo "<div class='success'>✅ Dossier trouvé : $path</div>";
                
                // Chercher des fichiers de configuration
                $config_files = [
                    '*.ini',
                    '*.cfg',
                    '*.conf',
                    'HFConf.ini',
                    'MantaManager.ini',
                    'HFSQLServer.ini'
                ];
                
                foreach ($config_files as $pattern) {
                    $files = glob($path . '\**\' . $pattern, GLOB_BRACE);
                    if ($files) {
                        foreach (array_slice($files, 0, 5) as $file) {
                            echo "<div class='info'>Fichier config : " . basename($file) . " dans " . dirname($file) . "</div>";
                            $found_configs[] = $file;
                        }
                    }
                }
            }
        }
        
        // Lire quelques fichiers de configuration trouvés
        if (!empty($found_configs)) {
            echo "<h3>Contenu des fichiers de configuration</h3>";
            foreach (array_slice($found_configs, 0, 3) as $config_file) {
                if (is_readable($config_file) && filesize($config_file) < 10000) {
                    echo "<h4>" . basename($config_file) . "</h4>";
                    $content = file_get_contents($config_file);
                    echo "<pre>" . htmlspecialchars(substr($content, 0, 1000)) . "</pre>";
                }
            }
        }
        echo "</div>";

        // Test 4: Vérification des ports alternatifs
        echo "<div class='test-section'>";
        echo "<h2>🔍 4. Recherche de ports HFSQL alternatifs</h2>";
        
        echo "<h3>Scan des ports courants HFSQL</h3>";
        $hfsql_ports = [4900, 4901, 4902, 4999, 5000, 8080, 8000, 1433, 3306, 5432];
        
        foreach ($hfsql_ports as $port) {
            $start_time = microtime(true);
            $test_socket = @fsockopen($config['server'], $port, $errno, $errstr, 2);
            $elapsed = round((microtime(true) - $start_time) * 1000, 2);
            
            if ($test_socket) {
                fclose($test_socket);
                echo "<div class='success'>✅ Port $port ouvert ({$elapsed}ms)</div>";
                
                // Test ODBC sur ce port
                $test_dsn = "Driver={HFSQL};Server={$config['server']};Port=$port;Database={$config['database']};UID={$config['username']};PWD={$config['password']}";
                $test_connection = @odbc_connect($test_dsn, $config['username'], $config['password']);
                if ($test_connection) {
                    echo "<div class='success'>🎉 CONNEXION ODBC RÉUSSIE SUR LE PORT $port !</div>";
                    odbc_close($test_connection);
                } else {
                    $error = odbc_errormsg();
                    echo "<div class='warning'>⚠️ Port $port ouvert mais ODBC échoue : " . htmlspecialchars(substr($error, 0, 100)) . "</div>";
                }
            } else {
                echo "<div class='info'>Port $port fermé ({$elapsed}ms)</div>";
            }
        }
        echo "</div>";

        // Test 5: Solutions recommandées
        echo "<div class='test-section'>";
        echo "<h2>💡 5. Solutions recommandées</h2>";
        
        echo "<div class='solution'>";
        echo "<h3>🔧 Solution 1 : Vérifier la configuration du serveur HFSQL</h3>";
        echo "<ol>";
        echo "<li><strong>Ouvrez le Centre de Contrôle HFSQL</strong> (HFSQL Control Center)</li>";
        echo "<li><strong>Vérifiez la configuration du serveur :</strong>";
        echo "<ul>";
        echo "<li>Le serveur est-il configuré pour accepter les connexions ODBC ?</li>";
        echo "<li>Le port d'écoute est-il bien 4900 ?</li>";
        echo "<li>L'adresse d'écoute est-elle 0.0.0.0 (toutes interfaces) ?</li>";
        echo "</ul></li>";
        echo "<li><strong>Vérifiez la base de données :</strong>";
        echo "<ul>";
        echo "<li>La base 'DataCafe' existe-t-elle ?</li>";
        echo "<li>Est-elle accessible via ODBC ?</li>";
        echo "</ul></li>";
        echo "<li><strong>Vérifiez l'utilisateur :</strong>";
        echo "<ul>";
        echo "<li>L'utilisateur 'admin' existe-t-il ?</li>";
        echo "<li>A-t-il les droits de connexion ODBC ?</li>";
        echo "</ul></li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h3>🔧 Solution 2 : Activer l'accès ODBC sur le serveur</h3>";
        echo "<p>Le serveur HFSQL doit être configuré pour accepter les connexions ODBC :</p>";
        echo "<ol>";
        echo "<li><strong>Dans le Centre de Contrôle HFSQL :</strong>";
        echo "<ul>";
        echo "<li>Allez dans Configuration → Serveur</li>";
        echo "<li>Vérifiez que l'accès ODBC est activé</li>";
        echo "<li>Vérifiez les paramètres de sécurité</li>";
        echo "</ul></li>";
        echo "<li><strong>Redémarrez le serveur HFSQL</strong> après modification</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h3>🔧 Solution 3 : Utiliser un autre port ou mode de connexion</h3>";
        echo "<p>Si le port 4900 ne fonctionne pas :</p>";
        echo "<ol>";
        echo "<li><strong>Vérifiez les résultats du scan de ports ci-dessus</strong></li>";
        echo "<li><strong>Essayez les ports trouvés ouverts</strong></li>";
        echo "<li><strong>Consultez la documentation HFSQL</strong> pour le mode de connexion ODBC</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h3>🔧 Solution 4 : Vérifier la version et compatibilité</h3>";
        echo "<ol>";
        echo "<li><strong>Version HFSQL :</strong> Quelle version utilisez-vous ?</li>";
        echo "<li><strong>Driver ODBC :</strong> Est-il compatible avec votre version de serveur ?</li>";
        echo "<li><strong>Architecture :</strong> Driver 64-bit pour PHP 64-bit</li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";

        // Test 6: Commandes de dépannage
        echo "<div class='test-section'>";
        echo "<h2>🛠️ 6. Commandes de dépannage</h2>";
        
        echo "<div class='info'>";
        echo "<h3>Commandes à exécuter manuellement</h3>";
        echo "<div class='command'>telnet 127.0.0.1 4900</div>";
        echo "<p>Pour tester la connexion directe au serveur</p>";
        
        echo "<div class='command'>netstat -ano | findstr :4900</div>";
        echo "<p>Pour voir quel processus écoute sur le port 4900</p>";
        
        echo "<div class='command'>tasklist | findstr /i hf</div>";
        echo "<p>Pour voir les processus HFSQL en cours</p>";
        
        echo "<div class='command'>sc query | findstr /i hfsql</div>";
        echo "<p>Pour voir les services HFSQL</p>";
        echo "</div>";
        echo "</div>";

        // Informations système
        echo "<div class='test-section'>";
        echo "<h2>ℹ️ Informations système</h2>";
        echo "<ul>";
        echo "<li><strong>Version PHP :</strong> " . PHP_VERSION . "</li>";
        echo "<li><strong>Architecture PHP :</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
        echo "<li><strong>OS :</strong> " . PHP_OS . "</li>";
        echo "<li><strong>Extensions ODBC :</strong> " . (extension_loaded('odbc') ? 'ODBC ✅' : 'ODBC ❌') . ", " . (extension_loaded('pdo_odbc') ? 'PDO_ODBC ✅' : 'PDO_ODBC ❌') . "</li>";
        echo "<li><strong>Date/Heure :</strong> " . date('Y-m-d H:i:s T') . "</li>";
        echo "</ul>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer le diagnostic</a>
                <a href="test_dsn_manuel.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔧 Test DSN manuel</a>
            </p>
        </div>
    </div>
</body>
</html>
