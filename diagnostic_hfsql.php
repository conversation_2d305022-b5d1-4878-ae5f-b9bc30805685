<?php
/**
 * Script de diagnostic avancé pour HFSQL WinDev
 * Effectue des tests approfondis de la connexion et de la configuration
 */

// Configuration
$config = [
    'server' => '127.0.0.1',
    'port' => '4900',
    'database' => 'DataCafe',
    'username' => 'admin',
    'password' => '',
    'timeout' => 10
];

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Diagnostic HFSQL WinDev</title></head><body>\n";
echo "<h1>🔍 Diagnostic HFSQL WinDev</h1>\n";

// Fonction pour afficher un résultat de test
function afficherResultat($test, $resultat, $details = '') {
    $icon = $resultat ? '✅' : '❌';
    $color = $resultat ? 'green' : 'red';
    echo "<p style='color: $color;'>$icon <strong>$test:</strong> " . ($resultat ? 'OK' : 'ÉCHEC') . "</p>\n";
    if ($details) {
        echo "<p style='margin-left: 20px; color: #666;'>$details</p>\n";
    }
}

// Test 1: Vérification de l'environnement PHP
echo "<h2>📋 Environnement PHP</h2>\n";

$php_version = phpversion();
afficherResultat("Version PHP", version_compare($php_version, '7.0', '>='), "Version: $php_version");

$odbc_loaded = extension_loaded('odbc');
afficherResultat("Extension ODBC", $odbc_loaded);

if ($odbc_loaded) {
    $odbc_version = phpversion('odbc');
    echo "<p style='margin-left: 20px; color: #666;'>Version ODBC: $odbc_version</p>\n";
}

// Test 2: Connectivité réseau
echo "<h2>🌐 Connectivité Réseau</h2>\n";

$ping_result = false;
if (function_exists('exec')) {
    $output = [];
    $return_code = 0;
    exec("ping -n 1 {$config['server']} 2>&1", $output, $return_code);
    $ping_result = ($return_code === 0);
    $ping_details = $ping_result ? "Serveur accessible" : "Serveur non accessible via ping";
} else {
    $ping_details = "Fonction exec() non disponible";
}

afficherResultat("Ping serveur", $ping_result, $ping_details);

// Test de port TCP
$port_test = @fsockopen($config['server'], $config['port'], $errno, $errstr, $config['timeout']);
$port_accessible = ($port_test !== false);
if ($port_test) fclose($port_test);

$port_details = $port_accessible ? "Port {$config['port']} ouvert" : "Port {$config['port']} fermé ou filtré ($errstr)";
afficherResultat("Port HFSQL", $port_accessible, $port_details);

// Test 3: Configuration ODBC
echo "<h2>🔧 Configuration ODBC</h2>\n";

// Vérifier les drivers ODBC disponibles
$drivers_available = [];
if (function_exists('odbc_drivers')) {
    $drivers = odbc_drivers();
    if ($drivers) {
        foreach ($drivers as $driver) {
            $drivers_available[] = $driver['DRIVER'];
        }
    }
}

$hfsql_driver_found = in_array('HFSQL ODBC Driver', $drivers_available) || 
                      in_array('HFSQL', $drivers_available);

afficherResultat("Driver HFSQL ODBC", $hfsql_driver_found, 
    $hfsql_driver_found ? "Driver trouvé" : "Driver HFSQL non trouvé dans la liste");

if (!empty($drivers_available)) {
    echo "<details><summary>Drivers ODBC disponibles (" . count($drivers_available) . ")</summary>\n";
    echo "<ul>\n";
    foreach ($drivers_available as $driver) {
        echo "<li>$driver</li>\n";
    }
    echo "</ul></details>\n";
}

// Test 4: Connexion HFSQL
echo "<h2>🔌 Test de Connexion HFSQL</h2>\n";

$dsn = "Driver={HFSQL ODBC Driver};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}";

echo "<p><strong>DSN:</strong> <code>$dsn</code></p>\n";

$connection_success = false;
$connection_details = "";

try {
    $start_time = microtime(true);
    $connection = odbc_connect($dsn, $config['username'], $config['password']);
    $connection_time = round((microtime(true) - $start_time) * 1000, 2);
    
    if ($connection) {
        $connection_success = true;
        $connection_details = "Connexion établie en {$connection_time}ms";
        
        // Test d'une requête simple
        echo "<h3>📊 Test de Requête</h3>\n";
        
        $query_tests = [
            "SELECT 1 AS test" => "Test de requête basique",
            "SELECT CURRENT_TIMESTAMP" => "Test de fonction système",
            "SHOW TABLES" => "Liste des tables"
        ];
        
        foreach ($query_tests as $query => $description) {
            try {
                $result = odbc_exec($connection, $query);
                if ($result) {
                    afficherResultat($description, true, "Requête exécutée avec succès");
                    
                    // Afficher quelques résultats pour la liste des tables
                    if (strpos($query, 'TABLES') !== false) {
                        echo "<details><summary>Résultats</summary>\n";
                        $count = 0;
                        while (odbc_fetch_row($result) && $count < 10) {
                            $table_name = odbc_result($result, 1);
                            echo "<p>- $table_name</p>\n";
                            $count++;
                        }
                        if ($count == 0) {
                            echo "<p><em>Aucune table trouvée ou accès restreint</em></p>\n";
                        }
                        echo "</details>\n";
                    }
                } else {
                    $error = odbc_errormsg($connection);
                    afficherResultat($description, false, "Erreur: $error");
                }
            } catch (Exception $e) {
                afficherResultat($description, false, "Exception: " . $e->getMessage());
            }
        }
        
        odbc_close($connection);
        
    } else {
        $error = odbc_errormsg();
        $connection_details = "Échec de connexion: $error";
    }
    
} catch (Exception $e) {
    $connection_details = "Exception: " . $e->getMessage();
}

afficherResultat("Connexion HFSQL", $connection_success, $connection_details);

// Test 5: Recommandations
echo "<h2>💡 Recommandations</h2>\n";

if (!$connection_success) {
    echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🔧 Actions à vérifier :</h3>\n";
    echo "<ol>\n";
    
    if (!$ping_result) {
        echo "<li><strong>Serveur inaccessible:</strong> Vérifiez que le serveur HFSQL est démarré et accessible</li>\n";
    }
    
    if (!$port_accessible) {
        echo "<li><strong>Port fermé:</strong> Vérifiez que le port 4900 est ouvert sur le serveur et le pare-feu</li>\n";
    }
    
    if (!$hfsql_driver_found) {
        echo "<li><strong>Driver manquant:</strong> Installez le driver ODBC HFSQL sur ce serveur web</li>\n";
    }
    
    echo "<li><strong>Identifiants:</strong> Vérifiez le nom d'utilisateur et mot de passe</li>\n";
    echo "<li><strong>Base de données:</strong> Vérifiez que la base 'DataCafe' existe</li>\n";
    echo "<li><strong>Permissions:</strong> Vérifiez les droits d'accès de l'utilisateur 'admin'</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
} else {
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>✅ <strong>Connexion réussie !</strong> Votre configuration HFSQL fonctionne correctement.</p>\n";
    echo "</div>\n";
}

// Informations système
echo "<h2>ℹ️ Informations Système</h2>\n";
echo "<ul>\n";
echo "<li><strong>Système:</strong> " . php_uname() . "</li>\n";
echo "<li><strong>Date/Heure:</strong> " . date('Y-m-d H:i:s T') . "</li>\n";
echo "<li><strong>Serveur Web:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Non défini') . "</li>\n";
echo "</ul>\n";

echo "</body></html>\n";
?>
