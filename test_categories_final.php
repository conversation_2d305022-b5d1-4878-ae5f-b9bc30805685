<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Catégories HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        
        /* Zone Répétée */
        .zone-repetee {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .ligne-categorie {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .ligne-categorie:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .photo-categorie {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .no-photo {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        .nom-categorie {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }
        
        .id-categorie {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            text-align: center;
            width: 100%;
        }
        
        .compteur {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
        }
        
        .code-windev {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Final - Équivalent PHP du code WinDev</h1>
        
        <div class="info">
            <h3>📋 Code WinDev original :</h3>
            <div class="code-windev">
                POUR TOUT Categorie<br>
                &nbsp;&nbsp;&nbsp;&nbsp;ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)<br>
                FIN
            </div>
        </div>

        <?php
        // === DONNÉES DE SIMULATION HFSQL ===
        // Ces données représentent votre table Categorie HFSQL
        $categories_hfsql = [
            ['IDCategorie' => 1, 'categories' => 'Boissons chaudes', 'photo' => 'images/boissons_chaudes.jpg'],
            ['IDCategorie' => 2, 'categories' => 'Boissons froides', 'photo' => 'images/boissons_froides.jpg'],
            ['IDCategorie' => 3, 'categories' => 'Pâtisseries', 'photo' => 'images/patisseries.jpg'],
            ['IDCategorie' => 4, 'categories' => 'Sandwichs', 'photo' => 'images/sandwichs.jpg'],
            ['IDCategorie' => 5, 'categories' => 'Salades', 'photo' => 'images/salades.jpg'],
            ['IDCategorie' => 6, 'categories' => 'Desserts', 'photo' => ''],
            ['IDCategorie' => 7, 'categories' => 'Snacks', 'photo' => 'images/snacks.jpg'],
            ['IDCategorie' => 8, 'categories' => 'Glaces', 'photo' => 'images/glaces.jpg'],
            ['IDCategorie' => 9, 'categories' => 'Viennoiseries', 'photo' => 'images/viennoiseries.jpg'],
            ['IDCategorie' => 10, 'categories' => 'Plats chauds', 'photo' => ''],
        ];

        // === SIMULATION DE LA ZONE RÉPÉTÉE ===
        $ZR_cat = []; // Équivalent de la zone répétée ZR_cat

        // Fonction équivalente à ZoneRépétéeAjouteLigne
        function ZoneRepeteeAjouteLigne(&$zone, $nom, $photo, $id) {
            $zone[] = [
                'nom' => $nom,
                'photo' => $photo,
                'id' => $id
            ];
        }

        // Fonction équivalente à ZoneRépétéeOccurrence
        function ZoneRepeteeOccurrence($zone) {
            return count($zone);
        }

        // === EXÉCUTION DU CODE ÉQUIVALENT ===
        
        echo "<div class='success'>";
        echo "<h2>✅ Exécution du code équivalent PHP</h2>";
        echo "<p>Simulation parfaite de votre code WinDev sans erreur de connexion</p>";
        echo "</div>";

        // Vider la zone répétée (équivalent de ZoneRépétéeSupprimeTout)
        $ZR_cat = [];

        // === POUR TOUT Categorie ===
        echo "<div class='info'>";
        echo "<h3>🔄 Exécution : POUR TOUT Categorie</h3>";
        echo "</div>";

        $compteur_execution = 0;
        foreach ($categories_hfsql as $Categorie) {
            // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
            ZoneRepeteeAjouteLigne(
                $ZR_cat,                    // ZR_cat
                $Categorie['categories'],   // Categorie.categories
                $Categorie['photo'],        // Categorie.photo
                $Categorie['IDCategorie']   // Categorie.IDCategorie
            );
            $compteur_execution++;
        }

        // === AFFICHAGE DES RÉSULTATS ===
        
        echo "<div class='compteur'>";
        echo "📊 Nombre de catégories chargées : " . ZoneRepeteeOccurrence($ZR_cat);
        echo "</div>";

        // Statistiques
        echo "<div class='stats'>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($categories_hfsql) . "</div>";
        echo "<div>Catégories dans HFSQL</div>";
        echo "</div>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . ZoneRepeteeOccurrence($ZR_cat) . "</div>";
        echo "<div>Lignes dans ZR_cat</div>";
        echo "</div>";
        
        $avec_photo = array_filter($ZR_cat, function($ligne) {
            return !empty($ligne['photo']);
        });
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($avec_photo) . "</div>";
        echo "<div>Avec photo</div>";
        echo "</div>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . $compteur_execution . "</div>";
        echo "<div>Itérations exécutées</div>";
        echo "</div>";
        echo "</div>";

        // === AFFICHAGE DE LA ZONE RÉPÉTÉE ===
        
        echo "<h2>📂 Zone Répétée - Résultat final</h2>";
        
        if (empty($ZR_cat)) {
            echo "<div class='error'>Aucune catégorie dans la zone répétée</div>";
        } else {
            echo "<div class='zone-repetee'>";
            
            foreach ($ZR_cat as $index => $ligne) {
                echo "<div class='ligne-categorie' onclick='alert(\"Catégorie ID: {$ligne['id']} - {$ligne['nom']}\")'>";
                
                // Photo
                if (!empty($ligne['photo']) && file_exists($ligne['photo'])) {
                    echo "<img src='" . htmlspecialchars($ligne['photo']) . "' alt='" . htmlspecialchars($ligne['nom']) . "' class='photo-categorie'>";
                } else {
                    // Icônes différentes selon la catégorie
                    $icone = '📷';
                    if (stripos($ligne['nom'], 'boisson') !== false) $icone = '☕';
                    elseif (stripos($ligne['nom'], 'pâtisserie') !== false) $icone = '🧁';
                    elseif (stripos($ligne['nom'], 'sandwich') !== false) $icone = '🥪';
                    elseif (stripos($ligne['nom'], 'salade') !== false) $icone = '🥗';
                    elseif (stripos($ligne['nom'], 'dessert') !== false) $icone = '🍰';
                    elseif (stripos($ligne['nom'], 'glace') !== false) $icone = '🍦';
                    
                    echo "<div class='no-photo'>$icone</div>";
                }
                
                // Nom
                echo "<div class='nom-categorie'>" . htmlspecialchars($ligne['nom']) . "</div>";
                
                // ID
                echo "<div class='id-categorie'>ID: " . htmlspecialchars($ligne['id']) . "</div>";
                
                echo "</div>";
            }
            
            echo "</div>";
        }
        ?>

        <div style="margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
            <h3>💻 Code PHP équivalent final :</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 5px; overflow-x: auto;"><code><?php echo htmlspecialchars('
// Données HFSQL (remplacez par votre API plus tard)
$categories_hfsql = [
    ["IDCategorie" => 1, "categories" => "Boissons chaudes", "photo" => "images/boissons_chaudes.jpg"],
    // ... autres catégories
];

// Zone répétée
$ZR_cat = [];

// POUR TOUT Categorie
foreach ($categories_hfsql as $Categorie) {
    // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
    $ZR_cat[] = [
        "nom" => $Categorie["categories"],
        "photo" => $Categorie["photo"],
        "id" => $Categorie["IDCategorie"]
    ];
}

// Affichage
foreach ($ZR_cat as $ligne) {
    echo "<div class=\"categorie\">";
    echo "<h3>" . $ligne["nom"] . "</h3>";
    echo "<span>ID: " . $ligne["id"] . "</span>";
    echo "</div>";
}

echo "Nombre: " . count($ZR_cat);
'); ?></code></pre>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>🎯 Prochaines étapes</h3>
            <ol>
                <li><strong>✅ Code PHP fonctionnel</strong> - Votre équivalent fonctionne parfaitement</li>
                <li><strong>🔧 Créer l'API WebDev</strong> - Pour remplacer les données de simulation</li>
                <li><strong>🔗 Connecter à l'API</strong> - Remplacer les données simulées par l'API</li>
                <li><strong>🎨 Personnaliser l'affichage</strong> - Adapter selon vos besoins</li>
            </ol>
            
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="categories_simple.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📝 Version simple</a>
            </p>
        </div>
    </div>
</body>
</html>
