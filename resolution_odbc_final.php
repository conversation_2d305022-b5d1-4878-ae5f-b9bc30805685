<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résolution ODBC HFSQL - Final</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .critical { color: #721c24; background-color: #f5c6cb; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 5px solid #dc3545; }
        .solution { background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 5px solid #17a2b8; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f8f9fa; }
        .dsn-code { font-family: monospace; font-size: 10px; max-width: 400px; word-break: break-all; }
        .result-success { background-color: #d4edda; color: #155724; }
        .result-error { background-color: #f8d7da; color: #721c24; }
        .result-warning { background-color: #fff3cd; color: #856404; }
        .command { background-color: #f8f9fa; padding: 8px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Résolution ODBC HFSQL - Approche Systématique</h1>

        <div class="critical">
            <h2>🎯 Objectif</h2>
            <p>Résoudre définitivement l'erreur <strong>"Chaîne de connexion insuffisante"</strong> pour accéder aux vraies catégories de DataCafe via ODBC.</p>
        </div>

        <?php
        // Configuration de test
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Test 1: Diagnostic complet du driver HFSQL
        echo "<div class='test-section'>";
        echo "<h2>🔍 1. Diagnostic du driver HFSQL</h2>";
        
        // Vérifier la version exacte du driver
        echo "<h3>Version et détails du driver</h3>";
        $driver_details = shell_exec('powershell -Command "Get-OdbcDriver | Where-Object {$_.Name -eq \'HFSQL\'} | Format-List Name, Version, Platform, Attribute" 2>nul');
        if ($driver_details) {
            echo "<div class='success'>✅ Driver HFSQL trouvé :</div>";
            echo "<pre>" . htmlspecialchars($driver_details) . "</pre>";
        } else {
            echo "<div class='error'>❌ Impossible d'obtenir les détails du driver</div>";
        }
        
        // Vérifier les fichiers DLL
        echo "<h3>Fichiers DLL du driver</h3>";
        $system_paths = [
            'C:\Windows\System32',
            'C:\Windows\SysWOW64',
            'C:\Program Files\Common Files\ODBC\Data Sources',
            'C:\Program Files (x86)\Common Files\ODBC\Data Sources'
        ];
        
        foreach ($system_paths as $path) {
            $dll_files = glob($path . '\*hf*.dll');
            if (!empty($dll_files)) {
                echo "<div class='info'>Fichiers HFSQL dans $path :</div>";
                foreach ($dll_files as $dll) {
                    $file_info = pathinfo($dll);
                    $size = filesize($dll);
                    $date = date('Y-m-d H:i:s', filemtime($dll));
                    echo "<div class='command'>" . $file_info['basename'] . " ($size bytes, $date)</div>";
                }
            }
        }
        echo "</div>";

        // Test 2: Test de connectivité réseau avancé
        echo "<div class='test-section'>";
        echo "<h2>🌐 2. Test de connectivité réseau avancé</h2>";
        
        echo "<h3>Test de connexion socket avec analyse du protocole</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 10);
        if ($socket) {
            echo "<div class='success'>✅ Connexion socket établie</div>";
            
            // Analyser la réponse du serveur
            stream_set_timeout($socket, 3);
            $initial_data = @fread($socket, 1024);
            
            if ($initial_data) {
                echo "<div class='info'>Données initiales reçues (" . strlen($initial_data) . " bytes) :</div>";
                echo "<div class='command'>" . htmlspecialchars(substr($initial_data, 0, 200)) . "</div>";
                
                // Analyser le type de protocole
                if (strpos($initial_data, 'HFSQL') !== false) {
                    echo "<div class='success'>✅ Protocole HFSQL détecté</div>";
                } elseif (strpos($initial_data, 'HTTP') !== false) {
                    echo "<div class='warning'>⚠️ Protocole HTTP détecté (pas HFSQL)</div>";
                } else {
                    echo "<div class='warning'>⚠️ Protocole non identifié</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ Aucune donnée initiale reçue</div>";
            }
            
            // Test d'envoi de commandes
            echo "<h4>Test de commandes HFSQL</h4>";
            $test_commands = [
                "CONNECT\r\n",
                "VERSION\r\n",
                "HELLO\r\n",
                "INFO\r\n"
            ];
            
            foreach ($test_commands as $cmd) {
                @fwrite($socket, $cmd);
                usleep(200000); // 0.2 seconde
                $response = @fread($socket, 512);
                if ($response) {
                    echo "<div class='info'>Commande '" . trim($cmd) . "' → Réponse (" . strlen($response) . " bytes)</div>";
                } else {
                    echo "<div class='warning'>Commande '" . trim($cmd) . "' → Aucune réponse</div>";
                }
            }
            
            fclose($socket);
        } else {
            echo "<div class='error'>❌ Connexion socket échouée : $errstr ($errno)</div>";
        }
        echo "</div>";

        // Test 3: Test ODBC avec toutes les variantes possibles
        echo "<div class='test-section'>";
        echo "<h2>🔧 3. Test ODBC exhaustif</h2>";
        
        if (extension_loaded('odbc')) {
            // Variantes de DSN basées sur la documentation HFSQL
            $dsn_variants = [
                // Variantes de base
                'Minimal' => "Driver={HFSQL}",
                'Serveur seul' => "Driver={HFSQL};Server={$config['server']}",
                'Serveur + Port' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']}",
                
                // Variantes avec base de données
                'Avec Database' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                'Avec Initial Catalog' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Initial Catalog={$config['database']}",
                
                // Variantes d'authentification
                'Auth vide' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID=;PWD=",
                'Auth admin' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}",
                'Trusted Connection' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Trusted_Connection=yes",
                
                // Variantes spécifiques HFSQL
                'Mode Client/Server' => "Driver={HFSQL};Mode=ClientServer;Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                'Protocol TCP' => "Driver={HFSQL};Protocol=TCP;Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                'Connection String' => "Driver={HFSQL};ConnectionString=Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                
                // Variantes avec options
                'Avec Timeout' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Timeout=30",
                'Avec Charset' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Charset=UTF8",
                'Avec Compression' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};Compression=1",
                
                // Variantes de format
                'Sans accolades' => "Driver=HFSQL;Server={$config['server']};Port={$config['port']};Database={$config['database']}",
                'Format URL' => "Driver={HFSQL};Server={$config['server']}:{$config['port']};Database={$config['database']}",
                'Avec espaces' => "Driver = {HFSQL}; Server = {$config['server']}; Port = {$config['port']}; Database = {$config['database']}",
                
                // Variantes d'adresse
                'Localhost' => "Driver={HFSQL};Server=localhost;Port={$config['port']};Database={$config['database']}",
                'IP complète' => "Driver={HFSQL};Server=127.0.0.1;Port={$config['port']};Database={$config['database']}",
                
                // Variantes expérimentales
                'Data Source' => "Driver={HFSQL};Data Source={$config['server']};Port={$config['port']};Database={$config['database']}",
                'Host/Port' => "Driver={HFSQL};Host={$config['server']};Port={$config['port']};Database={$config['database']}",
                'ServerName' => "Driver={HFSQL};ServerName={$config['server']};ServerPort={$config['port']};DatabaseName={$config['database']}"
            ];
            
            echo "<table>";
            echo "<tr><th>Variante</th><th>DSN</th><th>Résultat</th><th>Code Erreur</th><th>Message</th></tr>";
            
            $success_count = 0;
            $successful_dsns = [];
            
            foreach ($dsn_variants as $name => $dsn) {
                echo "<tr>";
                echo "<td><strong>$name</strong></td>";
                echo "<td class='dsn-code'>" . htmlspecialchars($dsn) . "</td>";
                
                // Test de connexion avec capture d'erreur détaillée
                $start_time = microtime(true);
                $connection = @odbc_connect($dsn, $config['username'], $config['password']);
                $elapsed = round((microtime(true) - $start_time) * 1000, 2);
                
                if ($connection) {
                    echo "<td class='result-success'>✅ SUCCÈS ({$elapsed}ms)</td>";
                    echo "<td class='result-success'>-</td>";
                    echo "<td class='result-success'>Connexion établie</td>";
                    
                    $successful_dsns[] = ['name' => $name, 'dsn' => $dsn];
                    $success_count++;
                    
                    // Test d'une requête sur les catégories
                    $query_result = @odbc_exec($connection, "SELECT COUNT(*) as nb FROM Categorie");
                    if ($query_result) {
                        $count = odbc_result($query_result, 1);
                        echo "<script>console.log('Catégories trouvées avec $name: $count');</script>";
                        odbc_free_result($query_result);
                    }
                    
                    odbc_close($connection);
                } else {
                    $error = odbc_errormsg();
                    $error_code = odbc_error();
                    
                    echo "<td class='result-error'>❌ Échec ({$elapsed}ms)</td>";
                    echo "<td class='result-error'>$error_code</td>";
                    
                    if (strpos($error, 'insuffisant') !== false) {
                        echo "<td class='result-warning'>Chaîne insuffisante</td>";
                    } elseif (strpos($error, 'introuvable') !== false) {
                        echo "<td class='result-error'>Driver non trouvé</td>";
                    } elseif (strpos($error, 'connexion') !== false) {
                        echo "<td class='result-warning'>Erreur connexion</td>";
                    } else {
                        echo "<td class='result-error'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div class='info'>";
            echo "<h3>📊 Résultats du test exhaustif</h3>";
            echo "<p><strong>Variantes testées :</strong> " . count($dsn_variants) . "</p>";
            echo "<p><strong>Connexions réussies :</strong> $success_count</p>";
            echo "</div>";
            
            if (!empty($successful_dsns)) {
                echo "<div class='success'>";
                echo "<h3>🎉 CONNEXIONS RÉUSSIES !</h3>";
                foreach ($successful_dsns as $success) {
                    echo "<h4>✅ {$success['name']}</h4>";
                    echo "<div class='command'>" . htmlspecialchars($success['dsn']) . "</div>";
                }
                echo "</div>";
            }
            
        } else {
            echo "<div class='error'>❌ Extension ODBC non chargée</div>";
        }
        echo "</div>";

        // Test 4: Si aucune connexion ne fonctionne, diagnostic approfondi
        if ($success_count == 0) {
            echo "<div class='test-section'>";
            echo "<h2>🔬 4. Diagnostic approfondi - Aucune connexion réussie</h2>";
            
            echo "<div class='critical'>";
            echo "<h3>🚨 Analyse de la situation</h3>";
            echo "<p>Aucune des " . count($dsn_variants) . " variantes de DSN n'a fonctionné. Cela indique un problème fondamental.</p>";
            echo "</div>";
            
            echo "<div class='solution'>";
            echo "<h3>🔧 Actions de résolution prioritaires</h3>";
            echo "<ol>";
            echo "<li><strong>Vérifiez la configuration du serveur HFSQL :</strong>";
            echo "<ul>";
            echo "<li>Le serveur accepte-t-il les connexions ODBC ?</li>";
            echo "<li>Y a-t-il des restrictions de sécurité ?</li>";
            echo "<li>Le port 4900 est-il le bon port ODBC ?</li>";
            echo "</ul></li>";
            
            echo "<li><strong>Testez avec l'outil d'administration HFSQL :</strong>";
            echo "<ul>";
            echo "<li>Connectez-vous avec les mêmes paramètres</li>";
            echo "<li>Vérifiez que la base DataCafe est accessible</li>";
            echo "<li>Vérifiez les permissions de l'utilisateur admin</li>";
            echo "</ul></li>";
            
            echo "<li><strong>Réinstallez le driver ODBC HFSQL :</strong>";
            echo "<ul>";
            echo "<li>Désinstallez la version actuelle</li>";
            echo "<li>Téléchargez la dernière version depuis PC SOFT</li>";
            echo "<li>Installez en tant qu'administrateur</li>";
            echo "<li>Redémarrez le serveur web</li>";
            echo "</ul></li>";
            
            echo "<li><strong>Contactez le support PC SOFT :</strong>";
            echo "<ul>";
            echo "<li>Avec les résultats de ce diagnostic</li>";
            echo "<li>Mentionnez que toutes les variantes échouent</li>";
            echo "<li>Demandez la configuration ODBC exacte</li>";
            echo "</ul></li>";
            echo "</ol>";
            echo "</div>";
            echo "</div>";
        }

        // Test 5: Si des connexions fonctionnent, test des vraies données
        if ($success_count > 0) {
            echo "<div class='test-section'>";
            echo "<h2>🎉 5. Test des vraies données</h2>";
            
            $best_dsn = $successful_dsns[0]['dsn'];
            echo "<div class='success'>";
            echo "<h3>✅ Utilisation du DSN fonctionnel</h3>";
            echo "<div class='command'>" . htmlspecialchars($best_dsn) . "</div>";
            echo "</div>";
            
            $connection = @odbc_connect($best_dsn, $config['username'], $config['password']);
            if ($connection) {
                echo "<h3>📂 Récupération des vraies catégories</h3>";
                
                $categories_reelles = [];
                $result = @odbc_exec($connection, "SELECT IDCategorie, categories, photo FROM Categorie");
                
                if ($result) {
                    echo "<div class='success'>✅ Requête sur table Categorie réussie</div>";
                    
                    while (odbc_fetch_row($result)) {
                        $categories_reelles[] = [
                            'IDCategorie' => odbc_result($result, 'IDCategorie'),
                            'categories' => odbc_result($result, 'categories'),
                            'photo' => odbc_result($result, 'photo')
                        ];
                    }
                    
                    echo "<div class='info'>";
                    echo "<h4>📊 Vraies catégories récupérées : " . count($categories_reelles) . "</h4>";
                    echo "<ul>";
                    foreach (array_slice($categories_reelles, 0, 5) as $cat) {
                        echo "<li>ID: {$cat['IDCategorie']} - " . htmlspecialchars($cat['categories']) . "</li>";
                    }
                    if (count($categories_reelles) > 5) {
                        echo "<li>... et " . (count($categories_reelles) - 5) . " autres</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                    
                    // Sauvegarder les vraies données pour utilisation future
                    file_put_contents('categories_odbc_success.json', json_encode($categories_reelles, JSON_PRETTY_PRINT));
                    echo "<div class='success'>✅ Vraies données sauvegardées dans 'categories_odbc_success.json'</div>";
                    
                    odbc_free_result($result);
                } else {
                    echo "<div class='error'>❌ Erreur lors de la requête : " . odbc_errormsg($connection) . "</div>";
                }
                
                odbc_close($connection);
            }
            echo "</div>";
        }
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Relancer le diagnostic</a>
                <?php if (file_exists('categories_odbc_success.json')): ?>
                <a href="categories_vraies_donnees.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">✅ Voir les vraies catégories</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</body>
</html>
