<?php
/**
 * Script de vérification détaillée de l'environnement PHP
 * Affiche les informations nécessaires pour installer ODBC
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='fr'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>Informations PHP - Diagnostic ODBC</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".info-box { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #007bff; }\n";
echo ".error-box { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #dc3545; }\n";
echo ".success-box { background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #28a745; }\n";
echo "table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
echo "th { background-color: #f2f2f2; }\n";
echo "code { background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>🔍 Diagnostic PHP pour ODBC</h1>\n";

// Informations de base
echo "<div class='info-box'>\n";
echo "<h2>📋 Informations de base</h2>\n";
echo "<table>\n";
echo "<tr><th>Paramètre</th><th>Valeur</th></tr>\n";
echo "<tr><td>Version PHP</td><td>" . phpversion() . "</td></tr>\n";
echo "<tr><td>SAPI</td><td>" . php_sapi_name() . "</td></tr>\n";
echo "<tr><td>Système</td><td>" . php_uname() . "</td></tr>\n";
echo "<tr><td>Architecture</td><td>" . (PHP_INT_SIZE * 8) . " bits</td></tr>\n";
echo "<tr><td>Fichier php.ini</td><td>" . (php_ini_loaded_file() ?: 'Non trouvé') . "</td></tr>\n";
echo "<tr><td>Répertoire extensions</td><td>" . ini_get('extension_dir') . "</td></tr>\n";
echo "</table>\n";
echo "</div>\n";

// Vérification ODBC
if (extension_loaded('odbc')) {
    echo "<div class='success-box'>\n";
    echo "<h2>✅ Extension ODBC</h2>\n";
    echo "<p><strong>Statut :</strong> Installée et chargée</p>\n";
    echo "<p><strong>Version :</strong> " . phpversion('odbc') . "</p>\n";
    echo "</div>\n";
} else {
    echo "<div class='error-box'>\n";
    echo "<h2>❌ Extension ODBC</h2>\n";
    echo "<p><strong>Statut :</strong> Non disponible</p>\n";
    echo "<p><strong>Action requise :</strong> Installation nécessaire</p>\n";
    echo "</div>\n";
}

// Extensions chargées
echo "<div class='info-box'>\n";
echo "<h2>🔌 Extensions chargées</h2>\n";
$extensions = get_loaded_extensions();
sort($extensions);

// Filtrer les extensions liées aux bases de données
$db_extensions = [];
$other_extensions = [];

foreach ($extensions as $ext) {
    $ext_lower = strtolower($ext);
    if (strpos($ext_lower, 'sql') !== false || 
        strpos($ext_lower, 'odbc') !== false || 
        strpos($ext_lower, 'pdo') !== false ||
        in_array($ext_lower, ['mysqli', 'pgsql', 'sqlite3', 'oci8', 'mongo', 'redis'])) {
        $db_extensions[] = $ext;
    } else {
        $other_extensions[] = $ext;
    }
}

if (!empty($db_extensions)) {
    echo "<h3>🗄️ Extensions de base de données :</h3>\n";
    echo "<ul>\n";
    foreach ($db_extensions as $ext) {
        $version = phpversion($ext);
        echo "<li><strong>$ext</strong>" . ($version ? " (v$version)" : "") . "</li>\n";
    }
    echo "</ul>\n";
}

echo "<details>\n";
echo "<summary>Voir toutes les extensions (" . count($extensions) . ")</summary>\n";
echo "<div style='columns: 3; margin-top: 10px;'>\n";
foreach ($extensions as $ext) {
    echo "<div>$ext</div>\n";
}
echo "</div>\n";
echo "</details>\n";
echo "</div>\n";

// Configuration PHP pertinente
echo "<div class='info-box'>\n";
echo "<h2>⚙️ Configuration PHP pertinente</h2>\n";
echo "<table>\n";
echo "<tr><th>Directive</th><th>Valeur</th></tr>\n";

$config_items = [
    'extension_dir' => 'Répertoire des extensions',
    'enable_dl' => 'Chargement dynamique',
    'max_execution_time' => 'Temps d\'exécution max',
    'memory_limit' => 'Limite mémoire',
    'display_errors' => 'Affichage erreurs',
    'log_errors' => 'Log des erreurs',
    'error_log' => 'Fichier de log'
];

foreach ($config_items as $directive => $description) {
    $value = ini_get($directive);
    echo "<tr><td>$description ($directive)</td><td>" . ($value ?: 'Non défini') . "</td></tr>\n";
}

echo "</table>\n";
echo "</div>\n";

// Vérification des fichiers d'extension
echo "<div class='info-box'>\n";
echo "<h2>📁 Vérification des fichiers d'extension</h2>\n";

$ext_dir = ini_get('extension_dir');
if ($ext_dir && is_dir($ext_dir)) {
    echo "<p><strong>Répertoire :</strong> $ext_dir</p>\n";
    
    $odbc_files = [];
    $files = scandir($ext_dir);
    
    foreach ($files as $file) {
        if (stripos($file, 'odbc') !== false) {
            $odbc_files[] = $file;
        }
    }
    
    if (!empty($odbc_files)) {
        echo "<h3>📄 Fichiers ODBC trouvés :</h3>\n";
        echo "<ul>\n";
        foreach ($odbc_files as $file) {
            $full_path = $ext_dir . DIRECTORY_SEPARATOR . $file;
            $size = filesize($full_path);
            echo "<li><strong>$file</strong> (" . number_format($size) . " octets)</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p style='color: #dc3545;'>❌ Aucun fichier ODBC trouvé dans le répertoire des extensions</p>\n";
    }
    
    // Lister quelques autres fichiers pour référence
    $dll_files = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'dll' || 
               pathinfo($file, PATHINFO_EXTENSION) === 'so';
    });
    
    if (!empty($dll_files)) {
        echo "<details>\n";
        echo "<summary>Autres extensions disponibles (" . count($dll_files) . ")</summary>\n";
        echo "<div style='columns: 2; margin-top: 10px;'>\n";
        foreach (array_slice($dll_files, 0, 20) as $file) {
            echo "<div>$file</div>\n";
        }
        if (count($dll_files) > 20) {
            echo "<div><em>... et " . (count($dll_files) - 20) . " autres</em></div>\n";
        }
        echo "</div>\n";
        echo "</details>\n";
    }
} else {
    echo "<p style='color: #dc3545;'>❌ Répertoire des extensions non accessible : $ext_dir</p>\n";
}

echo "</div>\n";

// Instructions spécifiques
echo "<div class='info-box'>\n";
echo "<h2>🔧 Instructions spécifiques à votre environnement</h2>\n";

$sapi = php_sapi_name();
$os = php_uname('s');

echo "<p><strong>Votre configuration :</strong> $sapi sur $os</p>\n";

if (stripos($os, 'Windows') !== false) {
    echo "<h3>Instructions pour Windows :</h3>\n";
    
    if (stripos($_SERVER['SERVER_SOFTWARE'] ?? '', 'Apache') !== false) {
        echo "<ol>\n";
        echo "<li>Localisez votre fichier php.ini : <code>" . (php_ini_loaded_file() ?: 'Utilisez phpinfo() complet') . "</code></li>\n";
        echo "<li>Recherchez la ligne <code>;extension=odbc</code> ou <code>;extension=php_odbc</code></li>\n";
        echo "<li>Supprimez le point-virgule au début de la ligne</li>\n";
        echo "<li>Redémarrez Apache</li>\n";
        echo "</ol>\n";
    } else {
        echo "<p>Pour votre serveur web, consultez la documentation spécifique.</p>\n";
    }
    
    if (empty($odbc_files)) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;'>\n";
        echo "<p><strong>⚠️ Fichier d'extension manquant</strong></p>\n";
        echo "<p>Le fichier php_odbc.dll n'a pas été trouvé. Vous devrez peut-être :</p>\n";
        echo "<ul>\n";
        echo "<li>Réinstaller PHP avec le support ODBC</li>\n";
        echo "<li>Télécharger l'extension compatible avec votre version PHP</li>\n";
        echo "<li>Utiliser un package comme XAMPP qui inclut ODBC</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
} else {
    echo "<h3>Instructions pour Linux :</h3>\n";
    echo "<div class='info-box'>\n";
    echo "<code>sudo apt install php-odbc</code> (Ubuntu/Debian)<br>\n";
    echo "<code>sudo yum install php-odbc</code> (CentOS/RHEL)<br>\n";
    echo "Puis redémarrez votre serveur web.\n";
    echo "</div>\n";
}

echo "</div>\n";

// Actions suivantes
echo "<div class='info-box'>\n";
echo "<h2>🚀 Actions suivantes</h2>\n";
echo "<ol>\n";
echo "<li><a href='install_odbc.php'>📖 Consultez le guide d'installation détaillé</a></li>\n";
if (extension_loaded('odbc')) {
    echo "<li><a href='index.php'>🔙 Retournez au test de connexion HFSQL</a></li>\n";
} else {
    echo "<li>Installez l'extension ODBC selon les instructions ci-dessus</li>\n";
    echo "<li><a href='" . $_SERVER['PHP_SELF'] . "'>🔄 Rechargez cette page pour vérifier</a></li>\n";
}
echo "</ol>\n";
echo "</div>\n";

// Lien vers phpinfo complet
echo "<div class='info-box'>\n";
echo "<h2>🔍 Diagnostic complet</h2>\n";
echo "<p><a href='?phpinfo=1' target='_blank'>Voir phpinfo() complet</a></p>\n";
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";

// Afficher phpinfo si demandé
if (isset($_GET['phpinfo'])) {
    echo "<hr><h1>phpinfo() complet</h1>";
    phpinfo();
}
?>
