// service_sync_windev.wl
// Service WinDev pour synchronisation automatique HFSQL → JSON

// À intégrer dans un service Windows WinDev ou une application WinDev avec timer

PROCÉDURE SynchroniserDonnees()

// Chemins des fichiers de synchronisation
sFichierCategories est une chaîne = "D:\xampp\htdocs\Becoffe\categories_live.json"
sFichierArticles est une chaîne = "D:\xampp\htdocs\Becoffe\articles_live.json"
sFichierStatus est une chaîne = "D:\xampp\htdocs\Becoffe\sync_status.json"

// === SYNCHRONISATION DES CATÉGORIES ===

sJSONCategories est une chaîne = "{"
sJSONCategories += """timestamp"": """ + DateHeureSys() + ""","
sJSONCategories += """source"": ""HFSQL_Live"","
sJSONCategories += """data"": ["

bPremier est un booléen = Vrai

POUR TOUT Categorie
    SI PAS bPremier ALORS
        sJSONCategories += ","
    FIN
    
    // Échapper les caractères spéciaux
    sNom est une chaîne = Remplace(Categorie.categories, """", "\""")
    sNom = Remplace(sNom, "\", "\\")
    sNom = Remplace(sNom, Caract(13), "\n")
    sNom = Remplace(sNom, Caract(10), "\r")
    
    sPhoto est une chaîne = Remplace(Categorie.photo, """", "\""")
    sPhoto = Remplace(sPhoto, "\", "\\")
    
    sJSONCategories += "{"
    sJSONCategories += """IDCategorie"": " + Categorie.IDCategorie + ","
    sJSONCategories += """categories"": """ + sNom + ""","
    sJSONCategories += """photo"": """ + sPhoto + """"
    sJSONCategories += "}"
    
    bPremier = Faux
FIN

sJSONCategories += "]"
sJSONCategories += "}"

// === SYNCHRONISATION DES ARTICLES ===

sJSONArticles est une chaîne = "{"
sJSONArticles += """timestamp"": """ + DateHeureSys() + ""","
sJSONArticles += """source"": ""HFSQL_Live"","
sJSONArticles += """data"": ["

bPremier = Vrai

POUR TOUT articles
    SI PAS bPremier ALORS
        sJSONArticles += ","
    FIN
    
    // Échapper les caractères spéciaux
    sDesignation est une chaîne = Remplace(articles.designation, """", "\""")
    sDesignation = Remplace(sDesignation, "\", "\\")
    sDesignation = Remplace(sDesignation, Caract(13), "\n")
    sDesignation = Remplace(sDesignation, Caract(10), "\r")
    
    sJSONArticles += "{"
    sJSONArticles += """IDarticles"": " + articles.IDarticles + ","
    sJSONArticles += """designation"": """ + sDesignation + ""","
    sJSONArticles += """IDCategorie"": " + articles.IDCategorie + ","
    sJSONArticles += """quantite"": " + articles.quantite + ""
    sJSONArticles += "}"
    
    bPremier = Faux
FIN

sJSONArticles += "]"
sJSONArticles += "}"

// === FICHIER DE STATUT ===

sJSONStatus est une chaîne = "{"
sJSONStatus += """last_sync"": """ + DateHeureSys() + ""","
sJSONStatus += """categories_count"": " + ZoneRépétéeOccurrence(ZR_CatTemp) + ","
sJSONStatus += """articles_count"": " + ZoneRépétéeOccurrence(ZR_ArtTemp) + ","
sJSONStatus += """status"": ""success"""
sJSONStatus += "}"

// === SAUVEGARDE DES FICHIERS ===

SI fSauveTexte(sFichierCategories, sJSONCategories) ET 
   fSauveTexte(sFichierArticles, sJSONArticles) ET
   fSauveTexte(sFichierStatus, sJSONStatus) ALORS
    
    // Log de succès
    Trace("Synchronisation réussie à " + DateHeureSys())
    
SINON
    // Log d'erreur
    Trace("Erreur synchronisation : " + ErreurInfo())
    
    // Fichier d'erreur
    sJSONErreur est une chaîne = "{"
    sJSONErreur += """last_sync"": """ + DateHeureSys() + ""","
    sJSONErreur += """status"": ""error"","
    sJSONErreur += """error"": """ + Remplace(ErreurInfo(), """", "\""") + """"
    sJSONErreur += "}"
    
    fSauveTexte(sFichierStatus, sJSONErreur)
FIN

FIN

// === PROCÉDURE PRINCIPALE POUR SERVICE ===

PROCÉDURE ServiceSynchronisation()

// Boucle infinie pour service Windows
TANTQUE Vrai
    
    // Synchroniser les données
    SynchroniserDonnees()
    
    // Attendre 30 secondes avant la prochaine synchronisation
    // Ajustez selon vos besoins (30s = temps réel, 5min = économique)
    Temporisation(30000)  // 30 secondes
    
FIN

FIN

// === UTILISATION DANS UNE APPLICATION WINDEV ===

// Code à mettre dans l'initialisation du projet ou dans un timer

// Timer de synchronisation (toutes les 30 secondes)
TimerSys("SynchroniserDonnees", 30000, tsRépétition)

// Ou synchronisation manuelle via bouton
// Code du bouton "Synchroniser"
SynchroniserDonnees()
Info("Synchronisation terminée")

/*
INSTRUCTIONS D'UTILISATION :

1. POUR UN SERVICE WINDOWS :
   - Créez un nouveau projet Service Windows dans WinDev
   - Copiez la procédure ServiceSynchronisation() dans le service
   - Compilez et installez le service
   - Le service synchronisera automatiquement toutes les 30 secondes

2. POUR UNE APPLICATION WINDEV EXISTANTE :
   - Ajoutez la procédure SynchroniserDonnees() à votre projet
   - Ajoutez un timer qui appelle cette procédure toutes les 30 secondes
   - Ou ajoutez un bouton pour synchronisation manuelle

3. CONFIGURATION :
   - Modifiez les chemins des fichiers selon votre installation
   - Ajustez la fréquence de synchronisation (30s, 1min, 5min...)
   - Ajoutez d'autres tables si nécessaire

4. CÔTÉ PHP :
   - Vos scripts PHP liront les fichiers JSON mis à jour automatiquement
   - Plus besoin d'export manuel !
*/
