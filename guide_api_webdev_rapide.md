# Guide API WebDev - Solution Temps Réel

## 🚀 Création rapide (15 minutes)

### Étape 1 : Nouveau projet WebDev
1. **Ouvrez WebDev**
2. **Nouveau projet** → Site WebDev dynamique
3. **Nom :** `API_DataCafe_Live`
4. **Importez votre analyse** HFSQL existante

### Étape 2 : Page API categories.awp

Créez une page AWP nommée `categories.awp` :

```windev
<%
// categories.awp - API temps réel pour catégories

// En-têtes CORS et JSON
PageAffiche("")
HTTPCodeRéponse(200)
HTTPEntête("Content-Type: application/json; charset=utf-8")
HTTPEntête("Access-Control-Allow-Origin: *")
HTTPEntête("Access-Control-Allow-Methods: GET, POST, OPTIONS")
HTTPEntête("Access-Control-Allow-Headers: Content-Type")

// Gestion CORS preflight
SI PageParamètre("REQUEST_METHOD") = "OPTIONS" ALORS
    RETOUR
FIN

// Construction JSON en temps réel
sJSON est une chaîne = "{"
sJSON += """error"": false,"
sJSON += """timestamp"": """ + DateHeureSys() + ""","
sJSON += """data"": ["

bPremier est un booléen = Vrai

// Lecture directe de la table Categorie (temps réel)
POUR TOUT Categorie
    SI PAS bPremier ALORS
        sJSON += ","
    FIN
    
    // Échapper les caractères spéciaux
    sNom est une chaîne = Remplace(Categorie.categories, """", "\""")
    sNom = Remplace(sNom, "\", "\\")
    sPhoto est une chaîne = Remplace(Categorie.photo, """", "\""")
    sPhoto = Remplace(sPhoto, "\", "\\")
    
    sJSON += "{"
    sJSON += """IDCategorie"": " + Categorie.IDCategorie + ","
    sJSON += """categories"": """ + sNom + ""","
    sJSON += """photo"": """ + sPhoto + """"
    sJSON += "}"
    
    bPremier = Faux
FIN

sJSON += "],"
sJSON += """count"": " + ZoneRépétéeOccurrence(ZR_temp) + ""
sJSON += "}"

// Retourner le JSON
ChaineConstruitUTF8(sJSON)
%>
```

### Étape 3 : Page API articles.awp

```windev
<%
// articles.awp - API temps réel pour articles

PageAffiche("")
HTTPCodeRéponse(200)
HTTPEntête("Content-Type: application/json; charset=utf-8")
HTTPEntête("Access-Control-Allow-Origin: *")

// Paramètre optionnel pour filtrer par catégorie
nCategorieID est un entier = Val(PageParamètre("categorie", "0"))

sJSON est une chaîne = "{"
sJSON += """error"": false,"
sJSON += """timestamp"": """ + DateHeureSys() + ""","
sJSON += """data"": ["

bPremier est un booléen = Vrai

// Lecture avec ou sans filtre
SI nCategorieID > 0 ALORS
    // Filtré par catégorie
    HFiltre(articles, IDCategorie, nCategorieID)
    POUR TOUT articles
        SI PAS bPremier ALORS sJSON += ","
        
        sDesignation est une chaîne = Remplace(articles.designation, """", "\""")
        
        sJSON += "{"
        sJSON += """IDarticles"": " + articles.IDarticles + ","
        sJSON += """designation"": """ + sDesignation + ""","
        sJSON += """IDCategorie"": " + articles.IDCategorie + ","
        sJSON += """quantite"": " + articles.quantite + ""
        sJSON += "}"
        
        bPremier = Faux
    FIN
    HDésactiveFiltre(articles)
SINON
    // Tous les articles
    POUR TOUT articles
        SI PAS bPremier ALORS sJSON += ","
        
        sDesignation est une chaîne = Remplace(articles.designation, """", "\""")
        
        sJSON += "{"
        sJSON += """IDarticles"": " + articles.IDarticles + ","
        sJSON += """designation"": """ + sDesignation + ""","
        sJSON += """IDCategorie"": " + articles.IDCategorie + ","
        sJSON += """quantite"": " + articles.quantite + ""
        sJSON += "}"
        
        bPremier = Faux
    FIN
FIN

sJSON += "]"
sJSON += "}"

ChaineConstruitUTF8(sJSON)
%>
```

### Étape 4 : Déploiement
1. **Générez** le site WebDev
2. **Déployez** sur le serveur d'application WebDev
3. **Configurez** le port (ex: 8080)

### Étape 5 : Test
- **Catégories :** `http://localhost:8080/categories.awp`
- **Articles :** `http://localhost:8080/articles.awp`
- **Articles par catégorie :** `http://localhost:8080/articles.awp?categorie=1`

## ✅ Avantages de cette solution

- ✅ **Temps réel** - Données toujours à jour
- ✅ **Accès natif HFSQL** - Aucun problème ODBC
- ✅ **Performance** - Accès direct aux données
- ✅ **Évolutif** - Facile d'ajouter d'autres tables
- ✅ **Standard REST** - Compatible avec tout langage

## 🔧 Temps de mise en œuvre

- **Création projet :** 5 minutes
- **Code API :** 5 minutes
- **Déploiement :** 5 minutes
- **Total :** 15 minutes

Une fois créée, cette API vous donne un accès permanent et en temps réel à vos données HFSQL !
