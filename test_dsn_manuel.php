<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test DSN Manuel HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .step { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #0056b3; }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #1e7e34; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-warning:hover { background-color: #e0a800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test DSN Manuel HFSQL</h1>

        <?php
        // Configuration par défaut
        $default_config = [
            'dsn_name' => 'TestHFSQL',
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];

        // Récupération des paramètres du formulaire
        $config = [
            'dsn_name' => $_POST['dsn_name'] ?? $default_config['dsn_name'],
            'server' => $_POST['server'] ?? $default_config['server'],
            'port' => $_POST['port'] ?? $default_config['port'],
            'database' => $_POST['database'] ?? $default_config['database'],
            'username' => $_POST['username'] ?? $default_config['username'],
            'password' => $_POST['password'] ?? $default_config['password']
        ];

        // Traitement des actions
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['list_dsn'])) {
                echo "<div class='info'>";
                echo "<h2>📋 DSN système existants</h2>";
                
                $list_command = 'Get-OdbcDsn -DsnType System | Format-Table Name, DriverName, Platform -AutoSize';
                $result = shell_exec('powershell -Command "' . $list_command . '" 2>&1');
                
                if ($result) {
                    echo "<pre>" . htmlspecialchars($result) . "</pre>";
                } else {
                    echo "<div class='warning'>⚠️ Impossible de lister les DSN système</div>";
                }
                echo "</div>";
            }
            
            if (isset($_POST['create_dsn'])) {
                echo "<div class='info'>";
                echo "<h2>🔧 Création du DSN système</h2>";
                
                // Supprimer le DSN s'il existe déjà
                $remove_command = 'Remove-OdbcDsn -Name "' . $config['dsn_name'] . '" -DsnType "System" -Platform "64-bit" -Force';
                shell_exec('powershell -Command "' . $remove_command . '" 2>nul');
                
                // Créer le nouveau DSN
                $create_command = 'Add-OdbcDsn -Name "' . $config['dsn_name'] . '" -DriverName "HFSQL" -DsnType "System" -Platform "64-bit" -SetPropertyValue @("Server=' . $config['server'] . '", "Port=' . $config['port'] . '", "Database=' . $config['database'] . '")';
                
                echo "<div class='code'>Commande PowerShell :<br>" . htmlspecialchars($create_command) . "</div>";
                
                $result = shell_exec('powershell -Command "' . $create_command . '" 2>&1');
                
                if ($result === null || trim($result) === '') {
                    echo "<div class='success'>✅ DSN créé avec succès</div>";
                    
                    // Vérifier la création
                    $verify_command = 'Get-OdbcDsn -Name "' . $config['dsn_name'] . '" -DsnType "System"';
                    $verify_result = shell_exec('powershell -Command "' . $verify_command . '" 2>&1');
                    
                    if ($verify_result && strpos($verify_result, $config['dsn_name']) !== false) {
                        echo "<div class='success'>✅ DSN vérifié dans le système</div>";
                        echo "<pre>" . htmlspecialchars($verify_result) . "</pre>";
                    } else {
                        echo "<div class='warning'>⚠️ DSN créé mais non trouvé lors de la vérification</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Erreur lors de la création :</div>";
                    echo "<pre>" . htmlspecialchars($result) . "</pre>";
                }
                echo "</div>";
            }
            
            if (isset($_POST['test_dsn'])) {
                echo "<div class='info'>";
                echo "<h2>🧪 Test de connexion DSN</h2>";
                
                $dsn_string = "DSN=" . $config['dsn_name'] . ";UID=" . $config['username'] . ";PWD=" . $config['password'];
                echo "<div class='code'>Chaîne de connexion :<br>" . htmlspecialchars($dsn_string) . "</div>";
                
                if (extension_loaded('odbc')) {
                    $start_time = microtime(true);
                    $connection = @odbc_connect($dsn_string, $config['username'], $config['password']);
                    $connection_time = round((microtime(true) - $start_time) * 1000, 2);
                    
                    if ($connection) {
                        echo "<div class='success'>✅ Connexion DSN réussie ! (Temps: {$connection_time}ms)</div>";
                        
                        // Test de requêtes
                        $test_queries = [
                            "SELECT 1 AS test_value" => "Test basique",
                            "SELECT CURRENT_TIMESTAMP AS current_time" => "Test fonction système",
                            "SELECT COUNT(*) AS table_count FROM INFORMATION_SCHEMA.TABLES" => "Test métadonnées"
                        ];
                        
                        foreach ($test_queries as $query => $description) {
                            $query_start = microtime(true);
                            $result = @odbc_exec($connection, $query);
                            $query_time = round((microtime(true) - $query_start) * 1000, 2);
                            
                            if ($result) {
                                echo "<div class='success'>✅ $description : OK ({$query_time}ms)</div>";
                                if (odbc_fetch_row($result)) {
                                    $value = odbc_result($result, 1);
                                    echo "<div class='info'>Résultat : " . htmlspecialchars($value) . "</div>";
                                }
                                odbc_free_result($result);
                            } else {
                                $error = odbc_errormsg($connection);
                                echo "<div class='warning'>⚠️ $description : Échec - $error</div>";
                            }
                        }
                        
                        // Test de liste des tables
                        echo "<h3>📋 Tables disponibles</h3>";
                        $tables_result = @odbc_tables($connection);
                        if ($tables_result) {
                            echo "<div class='success'>✅ Fonction odbc_tables() réussie</div>";
                            $table_count = 0;
                            echo "<ul>";
                            while (odbc_fetch_row($tables_result) && $table_count < 10) {
                                $table_name = odbc_result($tables_result, "TABLE_NAME");
                                $table_type = odbc_result($tables_result, "TABLE_TYPE");
                                echo "<li>$table_name ($table_type)</li>";
                                $table_count++;
                            }
                            echo "</ul>";
                            if ($table_count == 0) {
                                echo "<div class='warning'>⚠️ Aucune table trouvée</div>";
                            }
                            odbc_free_result($tables_result);
                        } else {
                            echo "<div class='warning'>⚠️ Impossible de lister les tables : " . odbc_errormsg($connection) . "</div>";
                        }
                        
                        odbc_close($connection);
                        echo "<div class='success'>✅ Connexion fermée proprement</div>";
                        
                    } else {
                        $error = odbc_errormsg();
                        echo "<div class='error'>❌ Échec de la connexion DSN : " . htmlspecialchars($error) . "</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Extension ODBC non disponible</div>";
                }
                echo "</div>";
            }
            
            if (isset($_POST['test_direct'])) {
                echo "<div class='info'>";
                echo "<h2>🔌 Test de connexion directe</h2>";
                
                $direct_dsn = "Driver={HFSQL};Server=" . $config['server'] . ";Port=" . $config['port'] . ";Database=" . $config['database'] . ";UID=" . $config['username'] . ";PWD=" . $config['password'];
                echo "<div class='code'>Chaîne directe :<br>" . htmlspecialchars($direct_dsn) . "</div>";
                
                if (extension_loaded('odbc')) {
                    $connection = @odbc_connect($direct_dsn, $config['username'], $config['password']);
                    
                    if ($connection) {
                        echo "<div class='success'>✅ Connexion directe réussie !</div>";
                        odbc_close($connection);
                    } else {
                        $error = odbc_errormsg();
                        echo "<div class='error'>❌ Échec de la connexion directe : " . htmlspecialchars($error) . "</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Extension ODBC non disponible</div>";
                }
                echo "</div>";
            }
        }
        ?>

        <div class="step">
            <h2>⚙️ Configuration de connexion</h2>
            <form method="post">
                <div class="form-group">
                    <label for="dsn_name">Nom du DSN :</label>
                    <input type="text" id="dsn_name" name="dsn_name" value="<?php echo htmlspecialchars($config['dsn_name']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="server">Serveur :</label>
                    <input type="text" id="server" name="server" value="<?php echo htmlspecialchars($config['server']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="port">Port :</label>
                    <input type="number" id="port" name="port" value="<?php echo htmlspecialchars($config['port']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="database">Base de données :</label>
                    <input type="text" id="database" name="database" value="<?php echo htmlspecialchars($config['database']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="username">Utilisateur :</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($config['username']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe :</label>
                    <input type="text" id="password" name="password" value="<?php echo htmlspecialchars($config['password']); ?>">
                </div>
                
                <div class="form-group">
                    <button type="submit" name="list_dsn">📋 Lister les DSN</button>
                    <button type="submit" name="create_dsn" class="btn-success">🔧 Créer le DSN</button>
                    <button type="submit" name="test_dsn" class="btn-warning">🧪 Tester DSN</button>
                    <button type="submit" name="test_direct">🔌 Test direct</button>
                </div>
            </form>
        </div>

        <div class="warning">
            <h3>📋 Instructions manuelles</h3>
            <p>Si la création automatique échoue, créez le DSN manuellement :</p>
            <ol>
                <li>Ouvrez <strong>"Sources de données ODBC (64 bits)"</strong> depuis le menu Démarrer</li>
                <li>Allez dans l'onglet <strong>"DSN Système"</strong></li>
                <li>Cliquez sur <strong>"Ajouter..."</strong></li>
                <li>Sélectionnez le driver <strong>"HFSQL"</strong></li>
                <li>Configurez avec les paramètres ci-dessus</li>
                <li>Testez la connexion dans l'interface ODBC</li>
                <li>Revenez ici et utilisez le bouton "Tester DSN"</li>
            </ol>
        </div>

        <div class="info">
            <h3>💻 Code final à utiliser</h3>
            <p>Une fois la connexion DSN réussie, utilisez ce code dans votre application :</p>
            <div class="code">
                &lt;?php<br>
                $dsn = "DSN=<?php echo htmlspecialchars($config['dsn_name']); ?>;UID=<?php echo htmlspecialchars($config['username']); ?>;PWD=<?php echo htmlspecialchars($config['password']); ?>";<br>
                $connection = odbc_connect($dsn, "<?php echo htmlspecialchars($config['username']); ?>", "<?php echo htmlspecialchars($config['password']); ?>");<br>
                <br>
                if ($connection) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;echo "Connexion réussie !";<br>
                &nbsp;&nbsp;&nbsp;&nbsp;// Votre code ici...<br>
                &nbsp;&nbsp;&nbsp;&nbsp;odbc_close($connection);<br>
                } else {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;echo "Erreur : " . odbc_errormsg();<br>
                }<br>
                ?&gt;
            </div>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="diagnostic_avance_hfsql.php" style="background-color: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔬 Diagnostic avancé</a>
            </p>
        </div>
    </div>
</body>
</html>
