<?php
/**
 * Classe HFSQLCategories
 * Équivalent PHP des opérations HFSQL sur la table Categorie
 */

class HFSQLCategories {
    private $apiUrl;
    private $categories = [];
    private $currentIndex = 0;
    
    public function __construct($apiUrl = null) {
        $this->apiUrl = $apiUrl ?: 'http://localhost:8080/api/categories.awp';
        $this->chargerCategories();
    }
    
    /**
     * Charge les catégories depuis l'API ou utilise des données de test
     */
    private function chargerCategories() {
        // Tentative de chargement depuis l'API
        if ($this->chargerDepuisAPI()) {
            return;
        }
        
        // Données de simulation si l'API n'est pas disponible
        $this->categories = [
            ['IDCategorie' => 1, 'categories' => 'Boissons chaudes', 'photo' => 'images/boissons_chaudes.jpg'],
            ['IDCategorie' => 2, 'categories' => 'Boissons froides', 'photo' => 'images/boissons_froides.jpg'],
            ['IDCategorie' => 3, 'categories' => 'Pâtisseries', 'photo' => 'images/patisseries.jpg'],
            ['IDCategorie' => 4, 'categories' => 'Sandwichs', 'photo' => 'images/sandwichs.jpg'],
            ['IDCategorie' => 5, 'categories' => 'Salades', 'photo' => 'images/salades.jpg'],
            ['IDCategorie' => 6, 'categories' => 'Desserts', 'photo' => ''],
            ['IDCategorie' => 7, 'categories' => 'Snacks', 'photo' => 'images/snacks.jpg'],
            ['IDCategorie' => 8, 'categories' => 'Glaces', 'photo' => 'images/glaces.jpg'],
        ];
    }
    
    /**
     * Charge les données depuis l'API HFSQL
     */
    private function chargerDepuisAPI() {
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => 'Accept: application/json',
                    'timeout' => 5
                ]
            ]);
            
            $response = @file_get_contents($this->apiUrl, false, $context);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                if (isset($data['error']) && !$data['error'] && isset($data['data'])) {
                    $this->categories = $data['data'];
                    return true;
                }
            }
        } catch (Exception $e) {
            // Silencieux, on utilisera les données de simulation
        }
        
        return false;
    }
    
    /**
     * Équivalent de "POUR TOUT Categorie"
     * Retourne un itérateur pour parcourir toutes les catégories
     */
    public function pourTout() {
        $this->currentIndex = 0;
        return $this;
    }
    
    /**
     * Implémentation de l'itérateur pour foreach
     */
    public function current() {
        return $this->categories[$this->currentIndex] ?? null;
    }
    
    public function key() {
        return $this->currentIndex;
    }
    
    public function next() {
        $this->currentIndex++;
    }
    
    public function rewind() {
        $this->currentIndex = 0;
    }
    
    public function valid() {
        return isset($this->categories[$this->currentIndex]);
    }
    
    /**
     * Équivalent de HLitPremier(Categorie, IDCategorie)
     */
    public function litPremier($cle = null, $valeur = null) {
        if ($cle && $valeur) {
            foreach ($this->categories as $index => $categorie) {
                if ($categorie[$cle] == $valeur) {
                    $this->currentIndex = $index;
                    return true;
                }
            }
            return false;
        }
        
        $this->currentIndex = 0;
        return !empty($this->categories);
    }
    
    /**
     * Équivalent de HTrouve(Categorie)
     */
    public function trouve() {
        return isset($this->categories[$this->currentIndex]);
    }
    
    /**
     * Équivalent de HLitSuivant(Categorie)
     */
    public function litSuivant() {
        $this->currentIndex++;
        return $this->trouve();
    }
    
    /**
     * Accès aux propriétés de l'enregistrement courant
     * Équivalent de Categorie.categories, Categorie.photo, etc.
     */
    public function __get($propriete) {
        if ($this->trouve()) {
            return $this->categories[$this->currentIndex][$propriete] ?? null;
        }
        return null;
    }
    
    /**
     * Retourne toutes les catégories
     */
    public function toutes() {
        return $this->categories;
    }
    
    /**
     * Retourne le nombre de catégories
     */
    public function compte() {
        return count($this->categories);
    }
    
    /**
     * Filtre les catégories selon un critère
     */
    public function filtre($champ, $valeur) {
        return array_filter($this->categories, function($cat) use ($champ, $valeur) {
            return stripos($cat[$champ], $valeur) !== false;
        });
    }
}

/**
 * Classe ZoneRepetee
 * Équivalent PHP d'une zone répétée WinDev
 */
class ZoneRepetee {
    private $lignes = [];
    private $nom;
    
    public function __construct($nom = 'ZR_default') {
        $this->nom = $nom;
    }
    
    /**
     * Équivalent de ZoneRépétéeSupprimeTout
     */
    public function supprimeTout() {
        $this->lignes = [];
    }
    
    /**
     * Équivalent de ZoneRépétéeAjouteLigne
     */
    public function ajouteLigne(...$valeurs) {
        $this->lignes[] = $valeurs;
    }
    
    /**
     * Équivalent de ZoneRépétéeOccurrence
     */
    public function occurrence() {
        return count($this->lignes);
    }
    
    /**
     * Affiche la zone répétée sous forme de grille
     */
    public function afficher($template = null) {
        if (empty($this->lignes)) {
            echo "<div class='zone-vide'>Aucun élément à afficher</div>";
            return;
        }
        
        echo "<div class='zone-repetee' id='{$this->nom}'>";
        
        foreach ($this->lignes as $index => $ligne) {
            if ($template && is_callable($template)) {
                $template($ligne, $index);
            } else {
                $this->afficherLigneDefaut($ligne, $index);
            }
        }
        
        echo "</div>";
    }
    
    /**
     * Affichage par défaut d'une ligne
     */
    private function afficherLigneDefaut($ligne, $index) {
        echo "<div class='ligne-repetee' data-index='$index'>";
        
        // Supposons que c'est pour les catégories : [nom, photo, id]
        if (count($ligne) >= 3) {
            $nom = htmlspecialchars($ligne[0]);
            $photo = $ligne[1];
            $id = $ligne[2];
            
            echo "<div class='categorie-card'>";
            
            if (!empty($photo) && file_exists($photo)) {
                echo "<img src='$photo' alt='$nom' class='categorie-photo'>";
            } else {
                echo "<div class='categorie-no-photo'>📷</div>";
            }
            
            echo "<h3>$nom</h3>";
            echo "<span class='categorie-id'>ID: $id</span>";
            echo "</div>";
        } else {
            // Affichage générique
            foreach ($ligne as $i => $valeur) {
                echo "<span class='valeur-$i'>" . htmlspecialchars($valeur) . "</span> ";
            }
        }
        
        echo "</div>";
    }
    
    /**
     * Retourne toutes les lignes
     */
    public function getLignes() {
        return $this->lignes;
    }
    
    /**
     * Retourne une ligne spécifique
     */
    public function getLigne($index) {
        return $this->lignes[$index] ?? null;
    }
}

// === EXEMPLE D'UTILISATION ===

if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Ce code s'exécute seulement si le fichier est appelé directement
    
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Test HFSQLCategories</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .zone-repetee { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; }
            .categorie-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; text-align: center; }
            .categorie-photo { width: 100%; height: 120px; object-fit: cover; border-radius: 5px; }
            .categorie-no-photo { width: 100%; height: 120px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 5px; }
            .categorie-id { font-size: 12px; color: #666; }
            .zone-vide { text-align: center; color: #666; padding: 20px; }
        </style>
    </head>
    <body>";
    
    echo "<h1>Test de la classe HFSQLCategories</h1>";
    
    // Initialisation
    $Categorie = new HFSQLCategories();
    $ZR_cat = new ZoneRepetee('ZR_cat');
    
    echo "<h2>Méthode 1 : Code équivalent exact</h2>";
    
    // Vider la zone répétée
    $ZR_cat->supprimeTout();
    
    // POUR TOUT Categorie
    foreach ($Categorie->pourTout() as $cat) {
        // ZoneRépétéeAjouteLigne(ZR_cat, Categorie.categories, Categorie.photo, Categorie.IDCategorie)
        $ZR_cat->ajouteLigne($Categorie->categories, $Categorie->photo, $Categorie->IDCategorie);
    }
    
    echo "<p>Nombre de catégories : " . $ZR_cat->occurrence() . "</p>";
    $ZR_cat->afficher();
    
    echo "<h2>Méthode 2 : Style WinDev avec HLitPremier</h2>";
    
    // Style WinDev classique
    if ($Categorie->litPremier()) {
        echo "<ul>";
        do {
            echo "<li>ID: {$Categorie->IDCategorie} - {$Categorie->categories}</li>";
        } while ($Categorie->litSuivant());
        echo "</ul>";
    }
    
    echo "<h2>Méthode 3 : Recherche spécifique</h2>";
    
    // Rechercher une catégorie par ID
    if ($Categorie->litPremier('IDCategorie', 1)) {
        echo "<p>Catégorie trouvée : {$Categorie->categories}</p>";
    }
    
    echo "</body></html>";
}
?>

<!--
UTILISATION DANS VOS PROJETS :

1. Inclusion simple :
   <?php
   require_once 'HFSQLCategories.php';
   
   $Categorie = new HFSQLCategories();
   $ZR_cat = new ZoneRepetee();
   
   // Code WinDev équivalent
   foreach ($Categorie->pourTout() as $cat) {
       $ZR_cat->ajouteLigne($Categorie->categories, $Categorie->photo, $Categorie->IDCategorie);
   }
   
   $ZR_cat->afficher();
   ?>

2. Style WinDev classique :
   <?php
   $Categorie = new HFSQLCategories();
   
   if ($Categorie->litPremier()) {
       do {
           echo $Categorie->categories . "<br>";
       } while ($Categorie->litSuivant());
   }
   ?>

3. Recherche :
   <?php
   $categories = $Categorie->filtre('categories', 'Boissons');
   foreach ($categories as $cat) {
       echo $cat['categories'] . "<br>";
   }
   ?>
-->
