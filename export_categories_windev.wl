// export_categories_windev.wl
// Code WinDev pour exporter les catégories vers un fichier JSON

PROCÉDURE ExporterCategories()

// Chemin du fichier d'export
sFichierExport est une chaîne = "D:\xampp\htdocs\Becoffe\categories_reelles.json"

// Construction du JSON
sJSON est une chaîne = "["
bPremier est un booléen = Vrai

// Parcourir toutes les catégories réelles
POUR TOUT Categorie
    SI PAS bPremier ALORS
        sJSON += ","
    FIN
    
    // Échapper les caractères spéciaux
    sNom est une chaîne = Remplace(Categorie.categories, """", "\""")
    sNom = Remplace(sNom, "\", "\\")
    sNom = Remplace(sNom, Caract(13), "\n")
    sNom = Remplace(sNom, Caract(10), "\r")
    
    sPhoto est une chaîne = Remplace(Categorie.photo, """", "\""")
    sPhoto = Remplace(sPhoto, "\", "\\")
    
    // Construire l'objet JSON pour cette catégorie
    sJSON += "{"
    sJSON += """IDCategorie"": " + Categorie.IDCategorie + ","
    sJSON += """categories"": """ + sNom + ""","
    sJSON += """photo"": """ + sPhoto + """"
    sJSON += "}"
    
    bPremier = Faux
FIN

sJSON += "]"

// Sauvegarder dans le fichier
SI fSauveTexte(sFichierExport, sJSON) ALORS
    Info("Export réussi vers : " + sFichierExport)
    Info("Nombre de catégories exportées : " + ZoneRépétéeOccurrence(ZR_Temp))
SINON
    Erreur("Erreur lors de l'export : " + ErreurInfo())
FIN

FIN

// Appeler la procédure
ExporterCategories()

/*
INSTRUCTIONS D'UTILISATION :

1. Copiez ce code dans une procédure WinDev
2. Exécutez-la depuis votre application WinDev
3. Le fichier categories_reelles.json sera créé
4. Votre PHP pourra alors lire ce fichier

Alternative - Code à mettre dans un bouton :
*/

// Code bouton WinDev pour export rapide
sFichier est une chaîne = "D:\xampp\htdocs\Becoffe\categories_reelles.json"
sContenu est une chaîne = "["
nCompteur est un entier = 0

POUR TOUT Categorie
    SI nCompteur > 0 ALORS
        sContenu += ","
    FIN
    
    sContenu += "{"
    sContenu += """IDCategorie"":" + Categorie.IDCategorie + ","
    sContenu += """categories"":""" + Remplace(Categorie.categories, """", "\""") + ""","
    sContenu += """photo"":""" + Remplace(Categorie.photo, """", "\""") + """"
    sContenu += "}"
    
    nCompteur++
FIN

sContenu += "]"

fSauveTexte(sFichier, sContenu)
Info("Export terminé : " + nCompteur + " catégories")
