<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Créer DSN Système HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .step { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Créer un DSN Système HFSQL</h1>

        <?php
        $config = [
            'dsn_name' => 'DataCafe_HFSQL',
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => '',
            'driver' => 'HFSQL'
        ];

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_dsn'])) {
            echo "<div class='info'>";
            echo "<h2>🔄 Tentative de création du DSN système...</h2>";
            
            // Commande PowerShell pour créer le DSN
            $powershell_command = 'Add-OdbcDsn -Name "' . $config['dsn_name'] . '" -DriverName "' . $config['driver'] . '" -DsnType "System" -SetPropertyValue @("Server=' . $config['server'] . '", "Port=' . $config['port'] . '", "Database=' . $config['database'] . '")';
            
            echo "<div class='code'>Commande PowerShell:<br>" . htmlspecialchars($powershell_command) . "</div>";
            
            $result = shell_exec('powershell -Command "' . $powershell_command . '" 2>&1');
            
            if ($result === null || trim($result) === '') {
                echo "<div class='success'>✅ DSN créé avec succès (aucune erreur retournée)</div>";
                
                // Vérifier si le DSN a été créé
                $verify_command = 'Get-OdbcDsn -Name "' . $config['dsn_name'] . '" -DsnType "System"';
                $verify_result = shell_exec('powershell -Command "' . $verify_command . '" 2>&1');
                
                if ($verify_result && strpos($verify_result, $config['dsn_name']) !== false) {
                    echo "<div class='success'>✅ DSN vérifié et trouvé dans le système</div>";
                } else {
                    echo "<div class='warning'>⚠️ DSN créé mais non trouvé lors de la vérification</div>";
                }
            } else {
                echo "<div class='error'>❌ Erreur lors de la création du DSN:</div>";
                echo "<div class='code'>" . htmlspecialchars($result) . "</div>";
            }
            echo "</div>";
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_dsn'])) {
            echo "<div class='info'>";
            echo "<h2>🧪 Test du DSN système...</h2>";
            
            $dsn_connection_string = "DSN=" . $config['dsn_name'] . ";UID=" . $config['username'] . ";PWD=" . $config['password'];
            echo "<div class='code'>Chaîne de connexion DSN:<br>" . htmlspecialchars($dsn_connection_string) . "</div>";
            
            if (extension_loaded('odbc')) {
                $connection = @odbc_connect($dsn_connection_string, $config['username'], $config['password']);
                
                if ($connection) {
                    echo "<div class='success'>✅ Connexion DSN réussie !</div>";
                    
                    // Test d'une requête simple
                    $query_result = @odbc_exec($connection, "SELECT 1 AS test");
                    if ($query_result) {
                        echo "<div class='success'>✅ Test de requête réussi</div>";
                    } else {
                        echo "<div class='warning'>⚠️ Connexion OK mais requête échouée</div>";
                    }
                    
                    odbc_close($connection);
                } else {
                    $error = odbc_errormsg();
                    echo "<div class='error'>❌ Échec de la connexion DSN: " . htmlspecialchars($error) . "</div>";
                }
            } else {
                echo "<div class='error'>❌ Extension ODBC non disponible</div>";
            }
            echo "</div>";
        }
        ?>

        <div class="info">
            <h2>📋 Configuration actuelle</h2>
            <ul>
                <li><strong>Nom du DSN:</strong> <?php echo htmlspecialchars($config['dsn_name']); ?></li>
                <li><strong>Driver:</strong> <?php echo htmlspecialchars($config['driver']); ?></li>
                <li><strong>Serveur:</strong> <?php echo htmlspecialchars($config['server']); ?></li>
                <li><strong>Port:</strong> <?php echo htmlspecialchars($config['port']); ?></li>
                <li><strong>Base de données:</strong> <?php echo htmlspecialchars($config['database']); ?></li>
                <li><strong>Utilisateur:</strong> <?php echo htmlspecialchars($config['username']); ?></li>
            </ul>
        </div>

        <div class="step">
            <h3>Étape 1: Créer le DSN système</h3>
            <p>Cliquez sur le bouton ci-dessous pour créer automatiquement un DSN système avec PowerShell :</p>
            <form method="post">
                <button type="submit" name="create_dsn" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">🔧 Créer le DSN système</button>
            </form>
        </div>

        <div class="step">
            <h3>Étape 2: Tester le DSN</h3>
            <p>Une fois le DSN créé, testez la connexion :</p>
            <form method="post">
                <button type="submit" name="test_dsn" style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">🧪 Tester la connexion DSN</button>
            </form>
        </div>

        <div class="warning">
            <h3>⚠️ Méthode manuelle alternative</h3>
            <p>Si la création automatique échoue, vous pouvez créer le DSN manuellement :</p>
            <ol>
                <li>Ouvrez <strong>"Sources de données ODBC (64 bits)"</strong> depuis le menu Démarrer</li>
                <li>Allez dans l'onglet <strong>"DSN Système"</strong></li>
                <li>Cliquez sur <strong>"Ajouter..."</strong></li>
                <li>Sélectionnez le driver <strong>"HFSQL"</strong> (s'il est disponible)</li>
                <li>Configurez avec les paramètres suivants :
                    <ul>
                        <li>Nom : <code><?php echo htmlspecialchars($config['dsn_name']); ?></code></li>
                        <li>Serveur : <code><?php echo htmlspecialchars($config['server']); ?></code></li>
                        <li>Port : <code><?php echo htmlspecialchars($config['port']); ?></code></li>
                        <li>Base : <code><?php echo htmlspecialchars($config['database']); ?></code></li>
                    </ul>
                </li>
                <li>Testez la connexion dans l'interface ODBC</li>
            </ol>
        </div>

        <div class="info">
            <h3>💻 Code à utiliser une fois le DSN créé</h3>
            <p>Une fois le DSN système créé et testé, utilisez ce code dans votre application :</p>
            <div class="code">
                &lt;?php<br>
                $dsn = "DSN=<?php echo htmlspecialchars($config['dsn_name']); ?>;UID=<?php echo htmlspecialchars($config['username']); ?>;PWD=<?php echo htmlspecialchars($config['password']); ?>";<br>
                $connection = odbc_connect($dsn, "<?php echo htmlspecialchars($config['username']); ?>", "<?php echo htmlspecialchars($config['password']); ?>");<br>
                <br>
                if ($connection) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;echo "Connexion réussie !";<br>
                &nbsp;&nbsp;&nbsp;&nbsp;// Votre code ici...<br>
                &nbsp;&nbsp;&nbsp;&nbsp;odbc_close($connection);<br>
                } else {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;echo "Erreur : " . odbc_errormsg();<br>
                }<br>
                ?&gt;
            </div>
        </div>

        <div class="error">
            <h3>🚨 Si le driver HFSQL n'est pas disponible</h3>
            <p>Si le driver HFSQL n'apparaît pas dans la liste des drivers ODBC :</p>
            <ol>
                <li><strong>Téléchargez et installez le driver HFSQL ODBC</strong> depuis le site PC SOFT</li>
                <li><strong>Vérifiez l'architecture</strong> : Assurez-vous d'installer la version 64 bits si votre PHP est en 64 bits</li>
                <li><strong>Redémarrez le serveur web</strong> après l'installation du driver</li>
                <li><strong>Vérifiez l'installation</strong> en ouvrant "Sources de données ODBC"</li>
            </ol>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🔄 Actions</h2>
            <p>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">🔄 Actualiser</a>
                <a href="diagnostic_complet.php" style="background-color: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔍 Diagnostic complet</a>
            </p>
        </div>
    </div>
</body>
</html>
