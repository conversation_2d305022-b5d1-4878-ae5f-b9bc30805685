<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Articles HFSQL - Basé sur votre solution</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        
        .zone-repetee {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .ligne-article {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .ligne-article:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .nom-article {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-article {
            font-size: 12px;
            color: #666;
            margin: 3px 0;
        }
        
        .quantite {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            display: inline-block;
        }
        
        .categorie-badge {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            display: inline-block;
            margin-left: 5px;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 Articles HFSQL - Solution PDO</h1>
        
        <div class="success">
            <h3>✅ Basé sur votre solution qui fonctionne !</h3>
            <p>Utilisation de PDO avec DSN système : <code>odbc:DataCafe</code></p>
        </div>

        <?php
        // Configuration identique à votre solution qui fonctionne
        $dsn = "odbc:DataCafe";
        $username = "admin";
        $password = "";

        // Paramètres de filtrage
        $categorie_filter = $_GET['categorie'] ?? '';
        $search_filter = $_GET['search'] ?? '';

        try {
            $pdo = new PDO($dsn, $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<div class='success'>✅ Connexion PDO établie avec succès</div>";
            
            // === RÉCUPÉRATION DES CATÉGORIES POUR LE FILTRE ===
            
            $sql_categories = "SELECT IDCategorie, categories FROM Categorie ORDER BY categories";
            $stmt_categories = $pdo->prepare($sql_categories);
            $stmt_categories->execute();
            $categories = $stmt_categories->fetchAll(PDO::FETCH_ASSOC);
            
            // === INTERFACE DE FILTRAGE ===
            
            echo "<div class='filter-section'>";
            echo "<h3>🔍 Filtres</h3>";
            echo "<form method='get'>";
            
            echo "<select name='categorie'>";
            echo "<option value=''>Toutes les catégories</option>";
            foreach ($categories as $cat) {
                $selected = ($categorie_filter == $cat['IDCategorie']) ? 'selected' : '';
                echo "<option value='{$cat['IDCategorie']}' $selected>" . htmlspecialchars($cat['categories']) . "</option>";
            }
            echo "</select>";
            
            echo "<input type='text' name='search' placeholder='Rechercher un article...' value='" . htmlspecialchars($search_filter) . "'>";
            echo "<button type='submit'>🔍 Filtrer</button>";
            echo "<a href='?' style='text-decoration: none;'><button type='button'>🔄 Tout afficher</button></a>";
            echo "</form>";
            echo "</div>";
            
            // === CONSTRUCTION DE LA REQUÊTE AVEC FILTRES ===
            
            $sql = "SELECT a.IDarticles, a.designation, a.IDCategorie, a.quantite, c.categories as nom_categorie 
                    FROM articles a 
                    LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie";
            
            $where_conditions = [];
            $params = [];
            
            if (!empty($categorie_filter)) {
                $where_conditions[] = "a.IDCategorie = ?";
                $params[] = $categorie_filter;
            }
            
            if (!empty($search_filter)) {
                $where_conditions[] = "a.designation LIKE ?";
                $params[] = '%' . $search_filter . '%';
            }
            
            if (!empty($where_conditions)) {
                $sql .= " WHERE " . implode(" AND ", $where_conditions);
            }
            
            $sql .= " ORDER BY a.designation";
            
            echo "<div class='info'>";
            echo "<h2>🔄 Exécution : POUR TOUT articles (avec filtres)</h2>";
            if (!empty($categorie_filter)) {
                $cat_name = '';
                foreach ($categories as $cat) {
                    if ($cat['IDCategorie'] == $categorie_filter) {
                        $cat_name = $cat['categories'];
                        break;
                    }
                }
                echo "<p>📂 Catégorie filtrée : <strong>$cat_name</strong></p>";
            }
            if (!empty($search_filter)) {
                echo "<p>🔍 Recherche : <strong>" . htmlspecialchars($search_filter) . "</strong></p>";
            }
            echo "</div>";
            
            // === EXÉCUTION DE LA REQUÊTE ===
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // === STATISTIQUES ===
            
            $total_articles = count($articles);
            $total_quantite = array_sum(array_column($articles, 'quantite'));
            
            echo "<div style='background: #007bff; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0;'>";
            echo "📊 Articles trouvés : $total_articles | Quantité totale : $total_quantite";
            echo "</div>";
            
            // === ZONE RÉPÉTÉE POUR LES ARTICLES ===
            
            echo "<h2>📦 Zone Répétée - Articles HFSQL</h2>";
            
            if (empty($articles)) {
                echo "<div class='error'>Aucun article trouvé avec les critères sélectionnés</div>";
            } else {
                echo "<div class='zone-repetee'>";
                
                foreach ($articles as $article) {
                    echo "<div class='ligne-article'>";
                    
                    // Nom de l'article
                    echo "<div class='nom-article'>" . htmlspecialchars($article['designation']) . "</div>";
                    
                    // Informations de l'article
                    echo "<div class='info-article'>";
                    echo "ID: " . $article['IDarticles'];
                    echo "</div>";
                    
                    echo "<div class='info-article'>";
                    echo "Catégorie: " . htmlspecialchars($article['nom_categorie'] ?? 'Non définie');
                    echo "<span class='categorie-badge'>ID: {$article['IDCategorie']}</span>";
                    echo "</div>";
                    
                    echo "<div class='info-article'>";
                    echo "Quantité: <span class='quantite'>{$article['quantite']}</span>";
                    echo "</div>";
                    
                    echo "</div>";
                }
                
                echo "</div>";
            }
            
            // === CODE ÉQUIVALENT WINDEV ===
            
            echo "<div style='margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;'>";
            echo "<h3>💻 Code WinDev équivalent :</h3>";
            echo "<pre style='background: #fff; padding: 15px; border-radius: 5px;'>";
            
            if (!empty($categorie_filter)) {
                echo "// Avec filtre par catégorie\n";
                echo "HFiltre(articles, IDCategorie, $categorie_filter)\n";
            }
            
            echo "POUR TOUT articles\n";
            echo "    ZoneRépétéeAjouteLigne(ZR_articles, articles.designation, articles.quantite, articles.IDarticles)\n";
            echo "FIN\n";
            
            if (!empty($categorie_filter)) {
                echo "HDésactiveFiltre(articles)";
            }
            
            echo "</pre>";
            echo "</div>";
            
            // Sauvegarde des données
            file_put_contents('articles_success.json', json_encode([
                'timestamp' => date('Y-m-d H:i:s'),
                'source' => 'HFSQL_PDO_Success',
                'filters' => [
                    'categorie' => $categorie_filter,
                    'search' => $search_filter
                ],
                'count' => $total_articles,
                'data' => $articles
            ], JSON_PRETTY_PRINT));
            
        } catch (PDOException $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Erreur de connexion PDO</h3>";
            echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>🔗 Navigation</h3>
            <p>
                <a href="categories_finales_optimisees.php" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">📂 Voir les catégories</a>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔄 Actualiser</a>
            </p>
        </div>
    </div>
</body>
</html>
