<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test DSN Alternatives - HFSQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .dsn-code { font-family: 'Courier New', monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
        .test-success { background-color: #d4edda; }
        .test-error { background-color: #f8d7da; }
        .test-warning { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Alternatives DSN pour HFSQL</h1>
        
        <?php
        if (!extension_loaded('odbc')) {
            echo "<div class='error'>❌ Extension ODBC non disponible. <a href='install_odbc.php'>Installer ODBC</a></div>";
            echo "</div></body></html>";
            exit;
        }
        
        // Configuration de base
        $config = [
            'server' => '127.0.0.1',
            'port' => '4900',
            'database' => 'DataCafe',
            'username' => 'admin',
            'password' => ''
        ];
        
        echo "<div class='info'>";
        echo "<h2>📋 Configuration testée</h2>";
        echo "<ul>";
        foreach ($config as $key => $value) {
            echo "<li><strong>" . ucfirst($key) . ":</strong> " . ($value ?: '(vide)') . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        // Différentes variantes de DSN à tester
        $dsn_variants = [
            // Variantes du driver HFSQL
            [
                'name' => 'HFSQL ODBC Driver (standard)',
                'dsn' => "Driver={HFSQL ODBC Driver};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'HFSQL (court)',
                'dsn' => "Driver={HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'HFSQL ODBC',
                'dsn' => "Driver={HFSQL ODBC};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'PC SOFT HFSQL',
                'dsn' => "Driver={PC SOFT HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'WinDev HFSQL',
                'dsn' => "Driver={WinDev HFSQL};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Variantes de paramètres
            [
                'name' => 'Sans spécifier la base',
                'dsn' => "Driver={HFSQL ODBC Driver};Server={$config['server']};Port={$config['port']};UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'Avec timeout',
                'dsn' => "Driver={HFSQL ODBC Driver};Server={$config['server']};Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']};Timeout=30"
            ],
            [
                'name' => 'Format alternatif 1',
                'dsn' => "Driver={HFSQL ODBC Driver};Data Source={$config['server']};Port={$config['port']};Initial Catalog={$config['database']};User ID={$config['username']};Password={$config['password']}"
            ],
            [
                'name' => 'Format alternatif 2',
                'dsn' => "Driver={HFSQL ODBC Driver};HOST={$config['server']};PORT={$config['port']};DB={$config['database']};USER={$config['username']};PASS={$config['password']}"
            ],
            
            // Test avec localhost
            [
                'name' => 'Localhost au lieu de 127.0.0.1',
                'dsn' => "Driver={HFSQL ODBC Driver};Server=localhost;Port={$config['port']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Test sans port (port par défaut)
            [
                'name' => 'Sans spécifier le port',
                'dsn' => "Driver={HFSQL ODBC Driver};Server={$config['server']};Database={$config['database']};UID={$config['username']};PWD={$config['password']}"
            ],
            
            // Test avec DSN nommé (si configuré)
            [
                'name' => 'DSN nommé (DataCafe)',
                'dsn' => "DSN=DataCafe;UID={$config['username']};PWD={$config['password']}"
            ],
            [
                'name' => 'DSN nommé (HFSQL)',
                'dsn' => "DSN=HFSQL;UID={$config['username']};PWD={$config['password']}"
            ]
        ];
        
        echo "<div class='info'>";
        echo "<h2>🧪 Test des variantes DSN</h2>";
        echo "<table>";
        echo "<tr><th style='width: 200px;'>Variante</th><th>DSN</th><th style='width: 100px;'>Résultat</th><th>Détails</th></tr>";
        
        $successful_dsn = null;
        $connection_details = [];
        
        foreach ($dsn_variants as $variant) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($variant['name']) . "</strong></td>";
            echo "<td class='dsn-code'>" . htmlspecialchars($variant['dsn']) . "</td>";
            
            // Test de connexion
            $start_time = microtime(true);
            $connection = @odbc_connect($variant['dsn'], $config['username'], $config['password']);
            $connection_time = round((microtime(true) - $start_time) * 1000, 2);
            
            if ($connection) {
                echo "<td class='test-success'>✅ Succès</td>";
                echo "<td class='test-success'>Connexion en {$connection_time}ms</td>";
                
                if (!$successful_dsn) {
                    $successful_dsn = $variant;
                    
                    // Test d'une requête simple
                    try {
                        $test_query = "SELECT 1 AS test";
                        $result = @odbc_exec($connection, $test_query);
                        if ($result) {
                            $connection_details[] = "✅ Requête SELECT réussie";
                        } else {
                            $connection_details[] = "⚠️ Connexion OK mais requête échouée: " . odbc_errormsg($connection);
                        }
                    } catch (Exception $e) {
                        $connection_details[] = "⚠️ Erreur de requête: " . $e->getMessage();
                    }
                }
                
                odbc_close($connection);
            } else {
                $error = odbc_errormsg();
                
                if (strpos($error, 'introuvable') !== false || strpos($error, 'not found') !== false) {
                    echo "<td class='test-error'>❌ Driver</td>";
                    echo "<td class='test-error'>Driver non trouvé</td>";
                } elseif (strpos($error, 'connexion') !== false || strpos($error, 'connect') !== false) {
                    echo "<td class='test-warning'>⚠️ Connexion</td>";
                    echo "<td class='test-warning'>Driver OK, erreur serveur</td>";
                } else {
                    echo "<td class='test-error'>❌ Erreur</td>";
                    echo "<td class='test-error'>" . htmlspecialchars(substr($error, 0, 50)) . "...</td>";
                }
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
        
        // Résultat du test
        if ($successful_dsn) {
            echo "<div class='success'>";
            echo "<h2>🎉 Connexion réussie !</h2>";
            echo "<p><strong>DSN fonctionnel :</strong></p>";
            echo "<div class='dsn-code'>" . htmlspecialchars($successful_dsn['dsn']) . "</div>";
            
            if (!empty($connection_details)) {
                echo "<h3>Détails de la connexion :</h3>";
                echo "<ul>";
                foreach ($connection_details as $detail) {
                    echo "<li>$detail</li>";
                }
                echo "</ul>";
            }
            
            echo "<h3>💻 Code PHP à utiliser :</h3>";
            echo "<div class='dsn-code'>";
            echo htmlspecialchars('$dsn = "' . $successful_dsn['dsn'] . '";') . "<br>";
            echo htmlspecialchars('$connection = odbc_connect($dsn, "' . $config['username'] . '", "' . $config['password'] . '");');
            echo "</div>";
            echo "</div>";
            
            // Mettre à jour le fichier index.php avec le DSN fonctionnel
            echo "<div class='info'>";
            echo "<h3>🔄 Mise à jour automatique</h3>";
            echo "<p><a href='?update_index=1' onclick='return confirm(\"Voulez-vous mettre à jour index.php avec ce DSN ?\")'>Mettre à jour index.php avec ce DSN</a></p>";
            echo "</div>";
            
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Aucune connexion réussie</h2>";
            echo "<p>Aucune des variantes DSN testées n'a fonctionné.</p>";
            echo "</div>";
            
            echo "<div class='warning'>";
            echo "<h3>🔧 Actions recommandées :</h3>";
            echo "<ol>";
            echo "<li><strong>Installer le driver HFSQL ODBC :</strong> <a href='odbc_drivers.php'>Voir le diagnostic des drivers</a></li>";
            echo "<li><strong>Vérifier le serveur HFSQL :</strong> Assurez-vous qu'il est démarré sur {$config['server']}:{$config['port']}</li>";
            echo "<li><strong>Vérifier la base de données :</strong> Confirmez que '{$config['database']}' existe</li>";
            echo "<li><strong>Tester la connectivité réseau :</strong> Ping {$config['server']} et telnet {$config['server']} {$config['port']}</li>";
            echo "</ol>";
            echo "</div>";
        }
        
        // Mise à jour du fichier index.php si demandé
        if (isset($_GET['update_index']) && $successful_dsn) {
            $index_content = file_get_contents('index.php');
            if ($index_content) {
                // Remplacer l'ancien DSN par le nouveau
                $old_pattern = '/\$dsn = "Driver=\{[^}]+\}[^"]*";/';
                $new_dsn_line = '$dsn = "' . $successful_dsn['dsn'] . '";';
                
                $updated_content = preg_replace($old_pattern, $new_dsn_line, $index_content);
                
                if ($updated_content && $updated_content !== $index_content) {
                    file_put_contents('index.php', $updated_content);
                    echo "<div class='success'>";
                    echo "<h3>✅ Fichier index.php mis à jour</h3>";
                    echo "<p><a href='index.php'>🔙 Tester la nouvelle configuration</a></p>";
                    echo "</div>";
                } else {
                    echo "<div class='warning'>";
                    echo "<h3>⚠️ Impossible de mettre à jour index.php automatiquement</h3>";
                    echo "<p>Copiez manuellement le DSN ci-dessus dans votre fichier.</p>";
                    echo "</div>";
                }
            }
        }
        
        // Informations supplémentaires
        echo "<div class='info'>";
        echo "<h2>ℹ️ Informations supplémentaires</h2>";
        
        echo "<h3>🔍 Diagnostic réseau :</h3>";
        $socket = @fsockopen($config['server'], $config['port'], $errno, $errstr, 5);
        if ($socket) {
            echo "<p>✅ Port {$config['port']} accessible sur {$config['server']}</p>";
            fclose($socket);
        } else {
            echo "<p>❌ Port {$config['port']} non accessible sur {$config['server']} ($errstr)</p>";
        }
        
        echo "<h3>🚀 Actions suivantes :</h3>";
        echo "<ul>";
        echo "<li><a href='odbc_drivers.php'>🔍 Diagnostic des drivers ODBC</a></li>";
        echo "<li><a href='index.php'>🔙 Retour au test principal</a></li>";
        echo "<li><a href='" . $_SERVER['PHP_SELF'] . "'>🔄 Relancer ce test</a></li>";
        echo "</ul>";
        
        echo "</div>";
        ?>
    </div>
</body>
</html>
