<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accès Direct HFSQL - Temps Réel</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background-color: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background-color: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background-color: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .solution { background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 5px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Solutions Temps Réel pour HFSQL</h1>

        <div class="info">
            <h2>🎯 Problème identifié</h2>
            <p>ODBC ne fonctionne pas, mais vous avez besoin d'un accès <strong>temps réel</strong> aux données qui changent fréquemment.</p>
        </div>

        <?php
        // Test des différentes solutions temps réel disponibles
        
        echo "<div class='solution'>";
        echo "<h2>🚀 Solution 1 : API WebDev (Recommandée)</h2>";
        
        // Test si l'API WebDev est disponible
        $api_urls = [
            'http://localhost:8080/categories.awp',
            'http://localhost:8000/categories.awp',
            'http://localhost:80/categories.awp'
        ];
        
        $api_found = false;
        foreach ($api_urls as $url) {
            $response = @file_get_contents($url, false, stream_context_create([
                'http' => ['timeout' => 2]
            ]));
            
            if ($response !== false) {
                $data = json_decode($response, true);
                if (isset($data['data'])) {
                    echo "<div class='success'>✅ API WebDev trouvée sur : $url</div>";
                    echo "<div class='info'>Nombre de catégories : " . count($data['data']) . "</div>";
                    $api_found = true;
                    break;
                }
            }
        }
        
        if (!$api_found) {
            echo "<div class='warning'>⚠️ API WebDev non trouvée</div>";
            echo "<div class='info'>";
            echo "<h3>📋 Pour créer l'API WebDev :</h3>";
            echo "<ol>";
            echo "<li>Suivez le guide dans <code>guide_api_webdev_rapide.md</code></li>";
            echo "<li>Temps de création : <strong>15 minutes</strong></li>";
            echo "<li>Accès temps réel permanent à vos données</li>";
            echo "</ol>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h2>🔄 Solution 2 : Synchronisation automatique</h2>";
        
        // Test si les fichiers de synchronisation existent
        $sync_files = [
            'categories_live.json' => 'Catégories synchronisées',
            'articles_live.json' => 'Articles synchronisés',
            'sync_status.json' => 'Statut de synchronisation'
        ];
        
        $sync_active = true;
        foreach ($sync_files as $file => $desc) {
            if (file_exists($file)) {
                $age = time() - filemtime($file);
                if ($age < 60) { // Moins d'1 minute
                    echo "<div class='success'>✅ $desc (mis à jour il y a {$age}s)</div>";
                } else {
                    echo "<div class='warning'>⚠️ $desc (mis à jour il y a " . round($age/60) . "min)</div>";
                }
            } else {
                echo "<div class='error'>❌ $desc non trouvé</div>";
                $sync_active = false;
            }
        }
        
        if (!$sync_active) {
            echo "<div class='info'>";
            echo "<h3>📋 Pour activer la synchronisation :</h3>";
            echo "<ol>";
            echo "<li>Utilisez le code dans <code>service_sync_windev.wl</code></li>";
            echo "<li>Intégrez dans votre application WinDev existante</li>";
            echo "<li>Synchronisation automatique toutes les 30 secondes</li>";
            echo "</ol>";
            echo "</div>";
        } else {
            // Afficher les données synchronisées
            echo "<h3>📂 Données synchronisées</h3>";
            
            if (file_exists('categories_live.json')) {
                $categories = json_decode(file_get_contents('categories_live.json'), true);
                if (isset($categories['data'])) {
                    echo "<div class='success'>✅ " . count($categories['data']) . " catégories synchronisées</div>";
                    echo "<div class='info'>Dernière sync : " . $categories['timestamp'] . "</div>";
                }
            }
        }
        echo "</div>";
        
        echo "<div class='solution'>";
        echo "<h2>🔌 Solution 3 : Résolution ODBC définitive</h2>";
        
        echo "<div class='warning'>";
        echo "<h3>📞 Contact support PC SOFT</h3>";
        echo "<p>Avec les informations de diagnostic :</p>";
        echo "<ul>";
        echo "<li><strong>22 variantes de DSN testées</strong> - toutes échouent</li>";
        echo "<li><strong>Erreur constante :</strong> 'Chaîne de connexion insuffisante'</li>";
        echo "<li><strong>Serveur HFSQL fonctionne</strong> (tables visibles)</li>";
        echo "<li><strong>Driver ODBC reconnu</strong> par Windows</li>";
        echo "</ul>";
        echo "<p><strong>Question précise :</strong> Comment activer l'accès ODBC sur serveur HFSQL ?</p>";
        echo "</div>";
        echo "</div>";
        
        // Recommandation finale
        echo "<div class='info'>";
        echo "<h2>💡 Ma recommandation</h2>";
        echo "<p><strong>Créez l'API WebDev</strong> - C'est la solution la plus robuste :</p>";
        echo "<ul>";
        echo "<li>✅ <strong>Temps réel</strong> - Données toujours à jour</li>";
        echo "<li>✅ <strong>Accès natif HFSQL</strong> - Aucun problème ODBC</li>";
        echo "<li>✅ <strong>Performance</strong> - Accès direct optimisé</li>";
        echo "<li>✅ <strong>Évolutif</strong> - Facile d'ajouter d'autres fonctionnalités</li>";
        echo "<li>✅ <strong>Standard REST</strong> - Compatible avec tout</li>";
        echo "</ul>";
        echo "<p><strong>Temps de création :</strong> 15 minutes</p>";
        echo "<p><strong>Bénéfice :</strong> Solution permanente et professionnelle</p>";
        echo "</div>";
        
        // Test de performance
        echo "<div class='solution'>";
        echo "<h2>⚡ Test de performance</h2>";
        
        echo "<h3>Comparaison des solutions :</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Solution</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Temps réel</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Performance</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Maintenance</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Fiabilité</th>";
        echo "</tr>";
        
        $solutions = [
            ['API WebDev', '✅ Immédiat', '⚡ Excellente', '✅ Aucune', '🛡️ Très haute'],
            ['Sync auto', '✅ 30 secondes', '⚡ Bonne', '⚠️ Modérée', '🛡️ Haute'],
            ['Export manuel', '❌ Manuel', '⚠️ Variable', '❌ Élevée', '⚠️ Faible'],
            ['ODBC', '✅ Immédiat', '⚡ Excellente', '✅ Aucune', '❌ Non fonctionnel']
        ];
        
        foreach ($solutions as $sol) {
            echo "<tr>";
            foreach ($sol as $cell) {
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>$cell</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        ?>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h2>🎯 Actions recommandées</h2>
            <ol>
                <li><strong>Immédiat :</strong> Créez l'API WebDev (15 min) avec le guide fourni</li>
                <li><strong>Alternative :</strong> Implémentez la synchronisation automatique</li>
                <li><strong>Long terme :</strong> Contactez PC SOFT pour résoudre ODBC</li>
            </ol>
            
            <p>
                <a href="guide_api_webdev_rapide.md" style="background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">📖 Guide API WebDev</a>
                <a href="service_sync_windev.wl" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">🔄 Code Synchronisation</a>
            </p>
        </div>
    </div>
</body>
</html>
